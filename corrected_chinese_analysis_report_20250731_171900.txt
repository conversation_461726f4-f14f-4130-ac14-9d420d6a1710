======================================================================
Spring AI Alibaba Jmanus 中文内容分析报告（修正版）
======================================================================
生成时间: 2025-07-31 17:19:00
分析目录: spring-ai-alibaba-jmanus/src/main/java

=== 统计结果 ===
总计 Java 文件数: 258
包含中文内容的文件数: 6
包含中文内容的文件占比: 2.33%

=== 中文内容分布分析 ===
仅在注释中包含中文: 1 个文件
仅在字符串中包含中文: 1 个文件
仅在标识符中包含中文: 2 个文件
混合类型（多种位置）: 2 个文件

=== 详细统计 ===
包含中文注释的文件: 3
包含中文字符串的文件: 2
包含中文标识符的文件: 3

=== 包含中文内容的文件列表 ===

文件: com/alibaba/cloud/ai/example/manus/config/RedirectController.java
  中文内容类型: 标识符
  包含中文的行数: 1
  示例行:
    第25行: * @desc jmanus首页重定向跳转

文件: com/alibaba/cloud/ai/example/manus/config/ManusProperties.java
  中文内容类型: 字符串
  包含中文的行数: 22
  示例行:
    第38行: description = "是否使用无头浏览器模式", defaultValue = "false", inputType = ConfigInputType.CHECKBOX,
    第39行: options = { @ConfigOption(value = "true", label = "是"), @ConfigOption(value = "false", label = "否") ...
    第56行: path = "manus.browser.requestTimeout", description = "浏览器请求超时时间(秒)", defaultValue = "180",
    ... 还有 19 行

文件: com/alibaba/cloud/ai/example/manus/dynamic/mcp/repository/McpConfigRepository.java
  中文内容类型: 标识符
  包含中文的行数: 3
  示例行:
    第35行: * 根据状态查询MCP配置列表
    第36行: * @param status MCP配置状态
    第37行: * @return 符合条件的MCP配置列表

文件: com/alibaba/cloud/ai/example/manus/dynamic/mcp/transport/StreamableHttpClientTransport.java
  中文内容类型: 注释
  包含中文的行数: 3
  示例行:
    第344行: // 安全地处理id字段，可能是String或Integer
    第347行: // 安全地处理method字段
    第350行: // 安全地处理jsonrpc字段

文件: com/alibaba/cloud/ai/example/manus/dynamic/mcp/service/McpCacheManager.java
  中文内容类型: 注释, 标识符
  包含中文的行数: 78
  示例行:
    第47行: * MCP缓存管理器 - 支持无闪断缓存更新
    第55行: * MCP连接结果封装类
    第115行: * 双缓存包装器 - 实现无闪断更新
    ... 还有 75 行

文件: com/alibaba/cloud/ai/example/manus/dynamic/prompt/model/enums/PromptEnum.java
  中文内容类型: 注释, 字符串
  包含中文的行数: 22
  示例行:
    第23行: // true, "用来做最终总结的prompt，对应任务结束以后告知用户的那个动作", "llm/finalize-system.txt"),
    第25行: // "用来做开始的用户任务分解用的prompt", "llm/manus-system.txt"),
    第29行: "构建执行计划的Prompt，如果分解任务做的不好，调这个",
    ... 还有 19 行