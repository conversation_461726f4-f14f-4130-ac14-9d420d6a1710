/*
 * Copyright 2024-2026 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import type { I18nType } from './type.ts'

const words: I18nType = {
  // 基础导航
  conversation: '对话',
  plan: '计划执行',
  backHome: '返回首页',
  noPageTip: '您访问的页面不存在。',

  // 初始化页面
  init: {
    welcome: '欢迎使用 JManus',
    welcomeStep: '欢迎使用 JManus',
    description: '首次使用需要配置 LLM 服务来启用 AI 功能。您可以选择使用阿里云百炼服务或自定义 OpenAI 兼容的 API 服务。',
    languageStepDescription: '请选择您的语言偏好，这将作为默认界面语言。',
    stepLanguage: '语言选择',
    stepModel: '模型配置',
    selectLanguageLabel: '选择语言',
    continueToModel: '继续配置模型',
    back: '返回',
    configModeLabel: '配置模式',
    dashscopeMode: '阿里云百炼（推荐）',
    dashscopeModeDesc: '使用阿里云百炼服务，只需提供 API 密钥即可快速开始',
    customMode: '自定义 OpenAI 兼容服务',
    customModeDesc: '配置任何兼容 OpenAI API 格式的服务，如 Ollama、LocalAI 等',
    apiKeyLabel: 'DashScope API 密钥',
    apiKeyPlaceholder: '请输入您的 API 密钥',
    apiKeyHint: '您可以从阿里云百炼控制台获取 API 密钥。',
    getApiKey: '获取 API 密钥',
    baseUrlLabel: 'API 基础地址',
    baseUrlPlaceholder: 'https://api.openai.com 或您的自定义地址',
    baseUrlHint: 'OpenAI 兼容的 API 基础地址，如 http://localhost:11434',
    customApiKeyLabel: 'API 密钥',
    customApiKeyPlaceholder: '请输入您的 API 密钥',
    modelNameLabel: '模型名称',
    modelNamePlaceholder: 'gpt-4.1 或您使用的模型名称',
    modelNameHint: '请输入您要使用的模型名称，如 gemini-2.5-pro、gpt-4.1 等',
    modelDisplayNameLabel: '模型显示名称（可选）',
    modelDisplayNamePlaceholder: '模型的显示名称',
    saveAndContinue: '保存并继续',
    saving: '保存中...',
    apiKeyRequired: 'API 密钥不能为空',
    baseUrlRequired: 'API 基础地址不能为空',
    modelNameRequired: '模型名称不能为空',
    saveFailed: '保存配置失败',
    networkError: '网络错误，请检查您的网络连接',
    successMessage: '配置保存成功！正在跳转到主页面...',
    restartRequired: 'API密钥已保存成功！为了使配置生效，需要重启应用程序。\n\n点击"确定"立即重启，点击"取消"稍后手动重启。',
  },

  // 通用按钮和操作
  common: {
    cancel: '取消',
    confirm: '确认',
    delete: '删除',
    edit: '编辑',
    save: '保存',
    reset: '重置',
    close: '关闭',
    add: '添加',
    create: '创建',
    update: '更新',
    submit: '提交',
    clear: '清空',
    search: '搜索',
    loading: '加载中...',
    success: '成功',
    error: '错误',
    warning: '警告',
    info: '信息',
    yes: '是',
    no: '否',
    enable: '启用',
    disable: '禁用',
    copy: '复制',
    paste: '粘贴',
    cut: '剪切',
    undo: '撤销',
    redo: '重做',
    select: '选择',
    selectAll: '全选',
    deselectAll: '取消全选',
    previous: '上一步',
    next: '下一步',
    finish: '完成',
    retry: '重试',
    refresh: '刷新',
    import: '导入',
    export: '导出',
    upload: '上传',
    download: '下载',
    preview: '预览',
    expand: '展开',
    collapse: '收起',
    maximize: '最大化',
    minimize: '最小化',
    fullscreen: '全屏',
    exitFullscreen: '退出全屏',
    parameters: '参数',
    thinking: '思考',
    input: '输入',
    actions: '操作',
  },

  // 配置相关
  config: {
    title: '配置管理',
    loading: '正在加载配置...',
    notFound: '未找到配置项',
    reset: '重置',
    resetGroupConfirm: '重置该组所有配置为默认值',
    modified: '已修改',
    saved: '配置已保存',
    saveFailed: '保存失败',
    search: '搜索配置项...',
    mcpSearch: '搜索MCP服务器...',
    mcpConfigPlaceholder: '请输入MCP服务器的配置(JSON格式)...',
    types: {
      string: '字符串',
      text: '文本',
      number: '数值',
      boolean: '布尔值',
      select: '选择',
      textarea: '多行',
      checkbox: '复选框',
    },
    range: '范围',
    min: '最小值',
    max: '最大值',
    categories: {
      basic: '基础配置',
      agent: 'Agent配置',
      model: 'Model配置',
      mcp: 'Tools/MCP配置',
      prompt: '动态Prompt配置',
      namespace: '命名空间配置',
    },
    subGroupDisplayNames: {
      agent: 'Agent',
      browser: '浏览器',
      interaction: '交互',
      system: '系统',
      performance: '性能',
      general: '通用',
      agents: '多智能体',
      infiniteContext: '无限上下文',
      filesystem: '文件系统',
      mcpServiceLoader: 'MCP服务加载器',
    },
    // Agent配置页面
    agentConfig: {
      title: 'Agent配置',
      import: '导入',
      export: '导出',
      configuredAgents: '已配置的Agent',
      agentCount: '个',
      noAgent: '暂无Agent配置',
      createNew: '新建Agent',
      selectAgentHint: '请选择一个Agent进行配置',
      newAgent: '新建Agent',
      agentName: 'Agent名称',
      agentNamePlaceholder: '输入Agent名称',
      description: '描述',
      descriptionPlaceholder: '描述这个Agent的功能和用途',
      nextStepPrompt: 'Agent提示词（人设，要求，以及下一步动作的指导）',
      nextStepPromptPlaceholder: '设置Agent的人设、要求以及下一步动作的指导...',
      modelConfiguration: '模型配置',
      modelConfigurationLabel: '选择模型',
      toolConfiguration: '工具配置',
      assignedTools: '已分配工具',
      noAssignedTools: '暂无分配的工具',
      addRemoveTools: '添加/删除工具',
      deleteConfirm: '删除确认',
      deleteConfirmText: '确定要删除',
      deleteWarning: '此操作不可恢复。',
      requiredFields: '请填写必要的字段',
      createSuccess: 'Agent创建成功',
      createFailed: '创建Agent失败',
      saveSuccess: 'Agent保存成功',
      saveFailed: '保存Agent失败',
      deleteSuccess: 'Agent删除成功',
      deleteFailed: '删除Agent失败',
      importSuccess: 'Agent导入成功',
      importFailed: '导入Agent失败',
      exportSuccess: 'Agent导出成功',
      exportFailed: '导出Agent失败',
      loadDataFailed: '加载数据失败',
      loadDetailsFailed: '加载Agent详情失败',
      invalidFormat: 'Agent配置格式不正确：缺少必要字段',
    },
    // Model配置页面
    modelConfig: {
      title: 'Model配置',
      import: '导入',
      export: '导出',
      configuredModels: '已配置的Model',
      modelCount: '个',
      noModel: '暂无Model配置',
      createNew: '新建Model',
      selectModelHint: '请选择一个Model进行配置',
      newModel: '新建Model',
      type: '模型类型',
      typePlaceholder: '选择模型类型',
      baseUrl: 'Base Url',
      baseUrlPlaceholder: '输入 Base Url',
      headers: '请求头信息',
      headersPlaceholder: '输入 Headers 需满足JSON对象格式',
      apiKey: 'API密钥',
      apiKeyPlaceholder: '输入API密钥',
      modelName: '模型名称',
      modelNamePlaceholder: '输入模型名称',
      description: '描述',
      descriptionPlaceholder: '输入模型描述',
      deleteConfirm: '删除确认',
      deleteConfirmText: '确定要删除',
      deleteWarning: '此操作不可恢复。',
      requiredFields: '请填写必要的字段',
      createSuccess: 'Model创建成功',
      createFailed: '创建Model失败',
      saveSuccess: 'Model保存成功',
      saveFailed: '保存Model失败',
      deleteSuccess: 'Model删除成功',
      deleteFailed: '删除Model失败',
      importSuccess: 'Model导入成功',
      importFailed: '导入Model失败',
      exportSuccess: 'Model导出成功',
      exportFailed: '导出Model失败',
      loadDataFailed: '加载数据失败',
      loadDetailsFailed: '加载Model详情失败',
      invalidFormat: 'Model配置格式不正确：缺少必要字段',
      validateConfig: '验证配置',
      validationSuccess: '验证成功',
      validationFailed: '验证失败',
      pleaseEnterBaseUrlAndApiKey: '请输入Base URL和API Key',
      selectModel: '选择模型',
      availableModels: '可用模型',
      searchModels: '搜索模型...',
      getModelsCount: '获取到 {count} 个可用模型',
      default: '默认',
      setAsDefault: '设为默认',
      currentDefault: '当前默认',
      setDefaultSuccess: '已成功设置为默认模型',
      setDefaultFailed: '设置默认模型失败',
      validatingBeforeSave: '保存前正在校验API密钥...',
      validationFailedCannotSave: 'API密钥校验失败，无法保存',
      temperature: '温度',
      temperaturePlaceholder: '留空使用模型默认值',
      topP: 'Top P',
      topPPlaceholder: '留空使用模型默认值',
    },
    // MCP配置页面
    mcpConfig: {
      title: 'MCP服务器配置',
      mcpServers: 'MCP服务器',
      addMcpServer: '添加MCP服务器',
      serverList: '服务器列表',
      noServers: '暂无MCP服务器配置',
      connectionType: '连接类型',
      configJsonLabel: 'mcp json配置：',
      configJsonPlaceholder: '请输入或粘贴MCP配置的JSON内容...',
      instructions: '使用说明：',
      instructionStep1: '找到你要用的mcp server的配置json：',
      instructionStep1Local: '本地(STDIO)',
      instructionStep1LocalDesc: '本地mcp server，目前市面上主流的是这个',
      instructionStep1Remote: '远程服务(SSE/STREAMING)',
      instructionStep1RemoteDesc:
        'mcp.higress.ai/ 上可以找到，有SSE和STREAMING两种，目前STREAM协议更完备一些',
      instructionStep2: '将json配置复制到上面的输入框，本地选STUDIO，远程选STREAMING或SSE，提交',
      instructionStep3: '这样mcp tools就注册成功了。',
      instructionStep4:
        '然后需要在Agent配置里面，新建一个agent，然后增加指定你刚才添加的mcp tools，这样可以极大减少冲突，增强tools被agent选择的准确性',
      configRequired: '请输入MCP服务器配置',
      invalidJson: '配置JSON格式不正确，请检查语法',
      addFailed: '添加失败',
      deleteFailed: '删除失败',
      deleteConfirm: '确定要删除这个MCP服务器配置吗？此操作不可恢复。',
      addSuccess: '添加成功',
      deleteSuccess: '删除成功',
      formatJson: '格式化JSON',
      jsonStatusEmpty: '请输入JSON配置...',
      jsonStatusValid: 'JSON格式有效',
      jsonStatusInvalid: 'JSON格式无效',
      missingMcpServers: '❌ 缺少mcpServers属性 - 请确保JSON包含mcpServers对象',
      invalidServerConfig: '❌ 服务器配置无效: {serverId} - 服务器配置必须是对象',
      invalidArgs: '❌ args字段必须是数组: {serverId} - 请将args改为数组格式',
      invalidEnv: '❌ env字段必须是对象: {serverId} - 请将env改为对象格式',
      invalidArgsType: '❌ args数组元素必须是字符串: {serverId}, 索引: {index} - 请确保所有参数都是字符串',
      invalidEnvType: '❌ env对象值必须是字符串: {serverId}, 键: {key} - 环境变量值必须是字符串',
      missingUrl: '❌ 缺少url字段: {serverId} - 没有command时必须有url',
      invalidUrl: '❌ url格式无效: {serverId} - 请检查URL格式是否正确',
      studioExample:
        '请输入MCP服务器配置JSON。\n\n例如：\n{\n  "mcpServers": {\n    "github": {\n      "command": "npx",\n      "args": [\n        "-y",\n        "@modelcontextprotocol/server-github"\n      ],\n      "env": {\n        "GITHUB_PERSONAL_ACCESS_TOKEN": "<YOUR_TOKEN>"\n      }\n    }\n  }\n}',
      sseExample:
        '请输入SSE MCP服务器配置JSON。\n\n例如：\n{\n  "mcpServers": {\n    "remote-server": {\n      "url": "https://example.com/mcp",\n      "headers": {\n        "Authorization": "Bearer <YOUR_TOKEN>"\n      }\n    }\n  }\n}',
      selectServerHint: '请选择左侧的MCP服务器，或点击新建MCP配置',
      jsonEditor: 'JSON编辑器',
      jsonConfigEmpty: 'JSON配置不能为空',
      jsonFormatError: 'JSON格式错误',
      jsonConfigSaved: 'JSON配置已保存',
      confirmDelete: '确认删除',
      deleteConfirmMessage: '确定要删除这个MCP服务器配置吗？此操作不可恢复。',
      deleteWarningText: '删除后无法恢复，请谨慎操作。',
      noServerSelected: '未选择MCP服务器',
      updateSuccess: '更新成功',
      saveFailed: '保存失败，请重试',
      operationFailed: '操作失败',
      mcpServerNamePlaceholder: '请输入MCP服务器名称',
      enabled: '启用',
      disabled: '禁用',
      newMcpConfig: '新建MCP配置',
      importAll: '全部导入',
      exportAll: '全部导出',
      command: 'Command',
      args: 'Args',
      env: 'Env',
      url: 'URL',
      save: '保存',
      delete: '删除',
      reset: '重置',
      import: '导入',
      cancel: '取消',
      exportSuccess: '导出成功',
      exportFailed: '导出失败',
      statusToggleSuccess: '状态切换成功',
      statusToggleFailed: '状态切换失败',
      missingUrlField: '缺少url字段: {serverId} - 没有command时必须有url或baseUrl',
      urlFieldTip: '💡 需要提供 url 或 baseUrl 字段',
      serverConfigWarning: 'Server {serverId} has no command but also no url or baseUrl',
      jsonSyntaxError: '❌ JSON语法错误 - 请检查括号、逗号、引号等符号是否正确',
      jsonIncomplete: '❌ JSON不完整 - 请检查是否缺少结束括号或引号',
      jsonNumberError: '❌ JSON数字格式错误 - 请检查数字格式',
      jsonStringError: '❌ JSON字符串格式错误 - 请检查引号是否配对',
      jsonSyntaxErrorWithMessage: '❌ JSON语法错误: {message}',
      correctFormatExample: '💡 正确格式示例: {"mcpServers": {"server-id": {"name": "服务器名称", "url": "服务器地址"}}}',
      commandPlaceholder: '例如: uvx',
      urlPlaceholder: '例如: https://mcp.example.com/server',
      argsPlaceholder: '每行一个参数，例如:\n--from\nmysql_mcp_server_pro\n--mode\nstdio',
      envPlaceholder: '键值对格式，每行一个，例如:\nMYSQL_HOST:127.0.0.1\nMYSQL_PORT:3306\nMYSQL_USER:root',
      connectionTypePlaceholder: '请选择连接类型',
      argsFormatError: 'Args格式错误，请输入有效的JSON数组',
      envFormatError: 'Env格式错误，请输入有效的JSON对象',
      argsStringError: 'Args格式错误，每个参数必须是字符串',
      envStringError: 'Env格式错误，每个值必须是字符串',
      importSuccess: '导入成功',
      importFailed: '导入失败',
      importInvalidJson: '导入的JSON无效',
    },
    // 基础配置
    basicConfig: {
      title: '基础配置',
      browserSettings: {
        headless: '是否使用无头浏览器模式',
        requestTimeout: '浏览器请求超时时间(秒)',
      },
      general: {
        debugDetail: 'debug模式 ：会要求模型输出更多内容，方便查找问题，但速度更慢',
        baseDir: 'manus根目录',
      },
      interactionSettings: {
        openBrowser: '启动时自动打开浏览器',
      },
      agentSettings: {
        maxSteps: '智能体执行最大步数',
        userInputTimeout: '用户输入表单等待超时时间(秒)',
        maxMemory: '能记住的最大消息数',
        parallelToolCalls: '并行工具调用',
      },
      agents: {
        forceOverrideFromYaml: '强制使用YAML配置文件覆盖同名Agent',
      },
      infiniteContext: {
        enabled: '是否开启无限上下文',
        parallelThreads: '并行处理线程数',
        taskContextSize: '触发无限上下文的字符数阈值(字符数)',
      },
      fileSystem: {
        allowExternalAccess: '是否允许文件操作超出工作目录',
      },
      mcpServiceLoader: {
        connectionTimeoutSeconds: 'MCP连接超时时间(秒)',
        maxRetryCount: 'MCP连接最大重试次数',
        maxConcurrentConnections: 'MCP最大并发连接数',
      },
      systemSettings: {
        systemName: '系统名称',
        language: '语言',
        maxThreads: '最大线程数',
        timeoutSeconds: '请求超时时间(秒)',
      },
      totalConfigs: '总配置数',
      modified: '已修改',
      exportConfigs: '导出配置',
      importConfigs: '导入配置',
      search: '搜索',
      loading: '加载中',
      saveSuccess: '配置保存成功',
      exportSuccess: '配置导出成功',
      exportFailed: '导出配置失败',
      invalidFormat: '配置文件格式不正确',
      loadConfigSuccess: '配置加载成功',
      resetSuccess: '配置重置成功',
      importSuccess: '配置导入成功',
      notFound: '未找到配置项',
      noModified: '没有需要保存的修改',
      resetGroupConfirm: '重置该组所有配置为默认值',
      isDefault: '该组配置已是默认值',
      reset: '重置',
      requestTimeout: '请求超时时间(秒)',
      browserTimeout: '浏览器请求超时时间(秒)',
      loadConfigFailed: '加载配置失败，请刷新重试',
      saveFailed: '保存失败，请重试',
      resetFailed: '重置失败，请重试',
      importFailed: '导入失败，请检查文件格式',
      groupDisplayNames: {
        manus: 'Manus',
        browser: '浏览器',
        interaction: '交互',
        system: '系统',
        performance: '性能',
      },
    },
    promptConfig: {
      title: '动态Prompt配置',
      configuredprompts: '已配置的Prompt',
      loadDetailsFailed: '加载Prompt详情失败',
      promptCount: '个',
      noPrompts: '没有配置的Prompt',
      createNew: '新建Prompt',
      promptName: 'Prompt名称',
      placeholder: '请输入',
      promptContent: '内容',
      messageType: '消息类型',
      type: '领域类型',
      builtIn: '内置',
      custom: '自定义',
      namespace: '命名空间',
      promptNamePlaceholder: '输入Prompt名称',
      selectPromptHint: '请选择一个prompt进行配置',
      promptContentPlaceholder: '请输入Prompt内容',
      descriptionPlaceholder: '输入Prompt功能和用途',
      description: '描述',
      requiredFields: '请填写必要的字段',
      newPrompt: '新建动态Prompt',
      saveSuccess: 'Prompt保存成功',
      saveFailed: 'Prompt保存失败',
      deleteSuccess: 'Prompt删除成功',
      deleteFailed: 'Prompt删除失败',
      deleteConfirm: '删除确认',
      deleteConfirmText: '确定要删除',
      deleteWarning: '此操作不可恢复。',
      exportSuccess: '配置导出成功',
      exportFailed: '导出配置失败',
      importSuccess: '配置导入成功',
      importFailed: '导入配置失败',
      resetToLanguageDefault: '重置为语言默认值',
      selectLanguage: '选择语言',
      resetToLanguageDefaultSuccess: '重置为语言默认值成功',
      resetToLanguageDefaultFailed: '重置为语言默认值失败',
      resetLanguageWarning: '此操作将覆盖当前内容为所选语言的默认版本',
      batchSwitchLanguage: '批量切换语言',
      batchSwitchLanguageSuccess: '批量切换语言成功',
      batchSwitchLanguageFailed: '批量切换语言失败',
      batchSwitchLanguageWarning: '此操作将覆盖所有Prompt的内容和描述为所选语言的默认版本',
    },
    namespaceConfig: {
      title: '命名空间配置',
      name: '命名空间名称',
      code: '命名空间编码',
      host:"域名",
      description: '命名空间描述',
      loadDetailsFailed: '加载namespace详情失败',
      selectNameSpaceHint: '请选择一个命名空间进行配置',
      createNew: '新建命名空间',
      placeholder: '请输入',
      saveSuccess: '保存成功',
      saveFailed: '保存失败',
      deleteSuccess: '删除成功',
      deleteFailed: '删除失败',
      deleteConfirm: '删除确认',
      deleteConfirmText: '确定要删除',
      deleteWarning: '此操作不可恢复。',
      configured: '已配置的命名空间',

      namespace: {
        selectNamespace: '请选择命名空间',
        namespace: '命名空间',
      },
    },
  },

  // Agent 配置
  agent: {
    title: 'Agent 配置',
    name: 'Agent名称',
    description: '描述',
    prompt: 'Agent提示词（人设，要求，以及下一步动作的指导）',
    tools: '工具',
    addAgent: '新建Agent',
    editAgent: '编辑Agent',
    deleteAgent: '删除Agent',
    deleteConfirm: '确定要删除吗？',
    deleteWarning: '此操作不可恢复。',
    namePlaceholder: '输入Agent名称',
    descriptionPlaceholder: '描述这个Agent的功能和用途',
    promptPlaceholder: '设置Agent的人设、要求以及下一步动作的指导...',
    toolSelection: '工具选择',
    availableTools: '可用工具',
    selectedTools: '已选工具',
    selectTools: '选择工具',
    required: '*',
    saveSuccess: 'Agent保存成功',
    saveFailed: 'Agent保存失败',
    deleteSuccess: 'Agent删除成功',
    deleteFailed: 'Agent删除失败',
  },

  // Model 配置
  model: {
    title: 'Model 配置',
    switch: '切换模型',
    name: 'Model名称',
    description: '描述',
    addModel: '新建Model',
    editModel: '编辑Model',
    deleteModel: '删除Model',
    deleteConfirm: '确定要删除吗？',
    deleteWarning: '此操作不可恢复。',
    namePlaceholder: '输入Model名称',
    descriptionPlaceholder: 'Model描述',
    required: '*',
    saveSuccess: 'Model保存成功',
    saveFailed: 'Model保存失败',
    deleteSuccess: 'Model删除成功',
    deleteFailed: 'Model删除失败',
  },

  // 计划模板配置
  planTemplate: {
    title: '计划模板配置',
    generator: '计划生成器',
    execution: '计划执行',
    prompt: '生成提示',
    promptPlaceholder: '描述您想要生成的计划...',
    generating: '生成中...',
    generate: '生成计划',
    updatePlan: '更新计划',
    executing: '执行中...',
    execute: '执行计划',
    executionParams: '执行参数',
    executionParamsPlaceholder: '输入执行参数（可选）...',
    apiUrl: 'API 调用地址',
    clearParams: '清空参数',
    versionControl: '版本控制',
    rollback: '回滚',
    restore: '恢复',
    currentVersion: '当前版本',
    saveTemplate: '保存模板',
    loadTemplate: '加载模板',
    templateSaved: '模板已保存',
    templateLoaded: '模板已加载',
    executionSuccess: '执行成功',
    executionFailed: '执行失败',
    generationSuccess: '生成成功',
    generationFailed: '生成失败',
  },

  // 聊天组件
  chat: {
    botName: 'TaskPilot:',
    thinkingLabel: 'TaskPilot 思考/处理',
    processing: '处理中...',
    step: '步骤',
    stepNumber: '步骤 {number}',
    stepExecutionDetails: '步骤执行详情',
    status: {
      executing: '执行中',
      completed: '已完成',
      pending: '待执行',
      failed: '失败',
    },
    userInput: {
      message: '请输入所需信息:',
      submit: '提交',
    },
    thinking: '正在思考...',
    thinkingAnalyzing: '正在分析任务需求...',
    thinkingExecuting: '正在执行: {title}',
    thinkingResponse: '正在组织语言回复您...',
    thinkingProcessing: '正在处理您的请求...',
    preparingExecution: '准备执行计划...',
    preparing: '准备执行...',
    response: '回复',
    retry: '重试',
    regenerate: '重新生成',
    copy: '复制',
    scrollToBottom: '滚动到底部',
    waitingDecision: '等待决策中',
    executionCompleted: '执行完成',
    noTool: '无工具',
    noToolParameters: '无工具参数',
    executionError: '执行出现错误',
    newMessage: '新消息',
    networkError: '网络连接有问题，请检查您的网络连接后再试一次',
    authError: '访问权限出现了问题，请联系管理员或稍后再试',
    formatError: '请求格式可能有些问题，能否请您重新表述一下您的需求？',
    unknownError: '处理您的请求时遇到了一些问题，请稍后再试',
    thinkingOutput: '思考输出',
  },

  // 输入组件
  input: {
    placeholder: '向 JManus 发送消息',
    send: '发送',
    planMode: 'PLAN-ACT计划模式',
    waiting: '等待任务完成...',
    maxLength: '最大长度',
    charactersRemaining: '剩余字符',
  },

  // 侧边栏
  sidebar: {
    title: 'PLAN-ACT 计划模板',
    templateList: '模板列表',
    configuration: '配置',
    newPlan: '新建计划',
    loading: '加载中...',
    retry: '重试',
    noTemplates: '没有可用的计划模板',
    unnamedPlan: '未命名计划',
    noDescription: '无描述',
    deleteTemplate: '删除此计划模板',
    jsonTemplate: 'JSON 模板',
    rollback: '回滚',
    restore: '恢复',
    jsonPlaceholder:
      'step2 ： 你可以在这里直接修改在step1中生产出的执行计划，让他更精准的按照你的希望执行。然后你可以点击执行计划，高确定性的执行这个计划',
    planGenerator: '计划生成器',
    generatorPlaceholder:
      'step1 : 在这里用自然语言输入你希望完成的任务，尽可能详细，然后点击生成计划，就可以生产一个可重复执行的精确计划',
    generating: '生成中...',
    generatePlan: '生成计划',
    updatePlan: '更新计划',
    executionController: '执行控制器',
    executionParams: '执行参数',
    executionParamsPlaceholder: '输入执行参数...',
    executionParamsHelp:
      '在重复执行时，你可以将step2里面的一些内容设置为变量，然后在这里指定该变量的具体值。例如json里面设置 变量1 ，然后在这里则设置 变量1=阿里巴巴 。 就可以实现类似函数的参数的效果。',
    clearParams: '清空参数',
    apiUrl: 'HTTP GET URL',
    statusApiUrl: '状态查询 API',
    executing: '执行中...',
    executePlan: '执行计划',
    newTemplate: '新建模板',
    templateName: '模板名称',
    templateDescription: '模板描述',
    lastModified: '最后修改',
    createTime: '创建时间',
    expand: '展开',
    collapse: '收起',
    pin: '固定',
    unpin: '取消固定',
    favorite: '收藏',
    unfavorite: '取消收藏',
    share: '分享',
    duplicate: '复制',
    rename: '重命名',
    move: '移动',
    archive: '归档',
    unarchive: '取消归档',
    selectTemplateFailed: '选择计划模板失败',
    confirmDelete: '确定要删除计划模板 "{name}" 吗？此操作不可恢复。',
    templateDeleted: '计划模板已删除。',
    deleteTemplateFailed: '删除计划模板失败',
    saveCompleted: '保存完成：{message}\n\n当前版本数：{versionCount}',
    saveSuccess: '保存成功：{message}\n\n当前版本数：{versionCount}',
    saveStatus: '保存状态：{message}',
    saveFailed: '保存计划修改失败',
    generateSuccess: '计划生成成功！模板ID: {templateId}',
    generateFailed: '生成计划失败',
    updateSuccess: '计划更新成功！',
    updateFailed: '更新计划失败',
    executeFailed: '执行计划失败',
    unknown: '未知',
    newTemplateName: '新建的执行计划',
    newTemplateDescription: '请使用计划生成器创建新的计划模板',
    generatedTemplateDescription: '通过生成器创建的计划模板',
    defaultExecutionPlanTitle: '执行计划',
  },

  // 模态框
  modal: {
    close: '关闭',
    cancel: '取消',
    confirm: '确认',
    save: '保存',
    delete: '删除',
    edit: '编辑',
  },

  // 编辑器
  editor: {
    format: '格式化',
    undo: '撤销',
    redo: '重做',
    find: '查找',
    replace: '替换',
    gotoLine: '跳转到行',
    selectAll: '全选',
    toggleWordWrap: '切换自动换行',
    toggleMinimap: '切换迷你地图',
    increaseFontSize: '增大字体',
    decreaseFontSize: '减小字体',
    resetFontSize: '重置字体大小',
  },

  // 语言切换
  language: {
    switch: '切换语言',
    current: '当前语言',
    zh: '中文',
    en: 'English',
  },

  // 主题
  theme: {
    switch: '切换主题',
    light: '浅色主题',
    dark: '深色主题',
    auto: '跟随系统',
  },

  // 错误页面
  error: {
    notFound: '页面未找到',
    notFoundDescription: '抱歉，您访问的页面不存在',
    serverError: '服务器错误',
    serverErrorDescription: '服务器出现了一些问题，请稍后再试',
    networkError: '网络错误',
    networkErrorDescription: '网络连接失败，请检查您的网络设置',
    backToHome: '返回首页',
    retry: '重试',
  },

  // 表单验证
  validation: {
    required: '此字段为必填项',
    email: '请输入有效的邮箱地址',
    phone: '请输入有效的手机号码',
    url: '请输入有效的网址',
    minLength: '至少需要 {min} 个字符',
    maxLength: '最多只能输入 {max} 个字符',
    min: '值不能小于 {min}',
    max: '值不能大于 {max}',
    pattern: '格式不正确',
    confirmation: '两次输入不一致',
  },

  // 时间相关
  time: {
    now: '刚刚',
    unknown: '未知时间',
    minuteAgo: '{count} 分钟前',
    hourAgo: '{count} 小时前',
    dayAgo: '{count} 天前',
    weekAgo: '{count} 周前',
    monthAgo: '{count} 个月前',
    yearAgo: '{count} 年前',
    today: '今天',
    yesterday: '昨天',
    tomorrow: '明天',
    thisWeek: '本周',
    lastWeek: '上周',
    nextWeek: '下周',
    thisMonth: '本月',
    lastMonth: '上月',
    nextMonth: '下月',
    thisYear: '今年',
    lastYear: '去年',
    nextYear: '明年',
  },

  // 数据统计
  stats: {
    total: '总计',
    count: '数量',
    percentage: '百分比',
    average: '平均',
    median: '中位数',
    min: '最小值',
    max: '最大值',
    sum: '总和',
    growth: '增长',
    decline: '下降',
    noData: '暂无数据',
    loading: '数据加载中...',
    error: '数据加载失败',
  },

  // 首页
  home: {
    welcomeTitle: '欢迎使用 JManus！',
    welcomeSubtitle: '您的 Java AI 智能助手，帮助您构建和完成各种任务。',
    tagline: 'Java AI 智能体',
    inputPlaceholder: '描述您想构建或完成的内容...',
    directButton: '直接进入工作台',
    examples: {
      stockPrice: {
        title: '查询股价',
        description: '获取今天阿里巴巴的最新股价（Agent可以使用浏览器工具）',
        prompt: '用浏览器基于百度，查询今天阿里巴巴的股价，并返回最新股价',
      },
      weather: {
        title: '查询天气',
        description: '获取北京今天的天气情况（Agent可以使用MCP工具服务）',
        prompt: '用浏览器，基于百度，查询北京今天的天气',
      },
      queryplan: {
        title: '查询一个人的信息',
        description: '查询 沈询 阿里的所有信息（用于展示无限上下文能力）',
        prompt: '用浏览器，基于百度，查询计划',
      },
      ainovel: {
        title: 'AI小说创作',
        description: '人工智能逐步击败人类主题小说（用于展示超长内容的输出）',
        prompt: '创建一个关于人工智能逐步击败人类的小说，包含10个章节',
      },
    },
  },

  // 右侧面板
  rightPanel: {
    stepExecutionDetails: '步骤执行详情',
    noStepSelected: '未选择执行步骤',
    selectStepHint: '请在左侧聊天区域选择一个执行步骤查看详情',
    stepExecuting: '步骤正在执行中，请稍候...',
    step: '步骤',
    executingAgent: '执行智能体',
    description: '描述',
    request: '请求',
    callingModel: '调用模型',
    executionResult: '执行结果',
    executing: '执行中...',
    thinkAndActionSteps: '思考与行动步骤',
    thinking: '思考',
    action: '行动',
    input: '输入',
    output: '输出',
    tool: '工具',
    toolParameters: '工具参数',
    noStepDetails: '暂无详细步骤信息',
    scrollToBottom: '滚动到底部',
    stepInfo: '步骤信息',
    stepName: '步骤名称',
    noExecutionInfo: '该步骤暂无详细执行信息',
    subPlan: '子执行计划',
    // 步骤状态
    status: {
      completed: '已完成',
      executing: '执行中',
      waiting: '等待执行',
    },
    // Tab 标签
    tabs: {
      details: '步骤执行详情',
      chat: 'Chat',
      code: 'Code',
    },
    // 示例 chatBubbles 数据
    chatBubbles: {
      analyzeRequirements: {
        title: '分析需求',
        content:
          '将您的请求分解为可操作的步骤：1) 创建用户实体，2) 实现用户服务，3) 构建 REST 端点，4) 添加验证和错误处理。',
      },
      generateCode: {
        title: '生成代码',
        content:
          '创建具有用户管理 CRUD 操作的 Spring Boot REST API。包括正确的 HTTP 状态代码和错误处理。',
      },
      codeGenerated: {
        title: '代码已生成',
        content:
          '成功生成具有所有 CRUD 操作的 UserController。代码包含正确的 REST 约定、错误处理，并遵循 Spring Boot 最佳实践。',
      },
    },
    // 时间显示
    timeAgo: {
      justNow: '刚刚',
      minutesAgo: '{n} 分钟前',
      hoursAgo: '{n} 小时前',
      daysAgo: '{n} 天前',
    },
    // 默认步骤标题
    defaultStepTitle: '步骤 {number}',
  },

  // 直接页面
  direct: {
    configuration: '配置',
    panelResizeHint: '拖拽调整面板大小，双击重置',
    aboutExecutionDetails: '关于集成执行详情',
  },

  // 定时任务
  cronTask: {
    title: '定时任务管理',
    addTask: '定时任务',
    noTasks: '暂无定时任务',
    taskName: '任务名称',
    taskNamePlaceholder: '请输入任务名称',
    cronExpression: 'Cron表达式',
    cronExpressionPlaceholder: '例如: 0 0 12 * * ?',
    cronExpressionHelp: '格式: 秒 分 时 日 月 周 年',
    taskDescription: '任务描述',
    taskDescriptionPlaceholder: '请输入任务描述',
    taskStatus: '任务状态',
    taskDetail: '任务详情',
    executeOnce: '执行一次',
    edit: '编辑',
    operations: '操作',
    enable: '启用',
    disable: '禁用',
    delete: '删除',
    deleteConfirm: '确认删除',
    deleteConfirmMessage: '确定要删除任务 "{taskName}" 吗？此操作不可撤销。',
    nextExecution: '下次执行时间',
    createTime: '创建时间',
    updateTime: '更新时间',
    active: '启用',
    inactive: '禁用',
    template: '示例：每天帮我早上8点，帮我收集当天的AI新闻吧',
    planTemplate: '计划模板',
    linkTemplate: '关联模板',
    noTemplate: '不关联',
    selectTemplate: '请选择模板',
    templateHelpText: '选择后，定时任务将按照制定好的计划执行',
    createTask: '创建定时任务',
    selectCreateMethod: '请选择创建方式',
    createWithJmanus: '让Jmanus帮忙创建',
    createWithJmanusDesc: '通过AI助手引导创建定时任务',
    createManually: '手动创建',
    createManuallyDesc: '自己填写定时任务信息',
  },
}

export default words
