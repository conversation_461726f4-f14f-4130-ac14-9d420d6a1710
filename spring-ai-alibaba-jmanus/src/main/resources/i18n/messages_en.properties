# English messages for Manus application

# ManusProperties - Browser Settings
manus.browser.headless.description=Whether to use headless browser mode
manus.browser.headless.option.true=Yes
manus.browser.headless.option.false=No
manus.browser.requestTimeout.description=Browser request timeout (seconds)

# ManusProperties - General Settings
manus.general.debugDetail.description=Debug mode: requires model to output more content for troubleshooting, but slower
manus.general.debugDetail.option.true=Yes
manus.general.debugDetail.option.false=No
manus.general.baseDir.description=Manus root directory

# ManusProperties - Interaction Settings
manus.interaction.openBrowser.description=Automatically open browser on startup
manus.interaction.openBrowser.option.true=Yes
manus.interaction.openBrowser.option.false=No

# ManusProperties - Agent Settings
manus.agent.maxSteps.description=Maximum execution steps for agent
manus.agent.forceOverrideFromYaml.description=Force override same-name Agent with YAML configuration
manus.agent.forceOverrideFromYaml.option.true=Yes
manus.agent.forceOverrideFromYaml.option.false=No
manus.agent.userInputTimeout.description=User input form timeout (seconds)
manus.agent.maxMemory.description=Maximum number of messages to remember
manus.agent.parallelToolCalls.description=Parallel tool calls
manus.agent.parallelToolCalls.option.true=Yes
manus.agent.parallelToolCalls.option.false=No

# ManusProperties - Infinite Context Settings
manus.infiniteContext.enabled.description=Whether to enable infinite context
manus.infiniteContext.enabled.option.true=Yes
manus.infiniteContext.enabled.option.false=No
manus.infiniteContext.parallelThreads.description=Number of parallel processing threads
manus.infiniteContext.taskContextSize.description=Character count threshold for triggering infinite context

# ManusProperties - File System Settings
manus.filesystem.allowExternalAccess.description=Whether to allow file operations outside the working directory
manus.filesystem.allowExternalAccess.option.true=Yes
manus.filesystem.allowExternalAccess.option.false=No

# ManusProperties - MCP Service Loader Settings
manus.mcpServiceLoader.connectionTimeoutSeconds.description=MCP connection timeout (seconds)
manus.mcpServiceLoader.maxRetryCount.description=MCP connection maximum retry count
manus.mcpServiceLoader.maxConcurrentConnections.description=MCP maximum concurrent connections

# PromptEnum Descriptions
prompt.PLANNING_PLAN_CREATION.description=Prompt for building execution plans, adjust this if task decomposition is not working well
prompt.AGENT_CURRENT_STEP_ENV.description=Defines current environment information, corresponding to results from all functions called by agent in the past, stored separately in agent
prompt.AGENT_STEP_EXECUTION.description=Context information given to agent during each execution step, most variables are preset and shouldn't be changed, can adjust some suggestions, a key agent step execution prompt
prompt.PLANNING_PLAN_FINALIZER.description=Prompt for final summary, corresponds to the action of informing users after task completion, merged with user request information
prompt.DIRECT_RESPONSE.description=Prompt for direct response mode, directly returns results when user requests don't need complex planning
prompt.AGENT_STUCK_ERROR.description=Error message when agent execution gets stuck
prompt.SUMMARY_PLAN_TEMPLATE.description=JSON template for content summary execution plan
prompt.MAPREDUCE_TOOL_DESCRIPTION.description=Description for MapReduce planning tool
prompt.MAPREDUCE_TOOL_PARAMETERS.description=Parameter definition JSON for MapReduce planning tool
prompt.AGENT_DEBUG_DETAIL_OUTPUT.description=Detailed output requirements for agent debug mode
prompt.AGENT_NORMAL_OUTPUT.description=Output requirements for agent normal mode
prompt.AGENT_PARALLEL_TOOL_CALLS_RESPONSE.description=Response rules for agent parallel tool calls
prompt.FORM_INPUT_TOOL_DESCRIPTION.description=Description for form input tool
prompt.FORM_INPUT_TOOL_PARAMETERS.description=Parameter definition JSON for form input tool
