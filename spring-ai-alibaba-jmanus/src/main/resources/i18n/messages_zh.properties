# ??????

# ManusProperties - ?????
manus.browser.headless.description=???????????
manus.browser.headless.option.true=?
manus.browser.headless.option.false=?
manus.browser.requestTimeout.description=?????????(?)

# ManusProperties - ????
manus.general.debugDetail.description=debug?? ?????????????????????????
manus.general.debugDetail.option.true=?
manus.general.debugDetail.option.false=?
manus.general.baseDir.description=manus???

# ManusProperties - ????
manus.interaction.openBrowser.description=??????????
manus.interaction.openBrowser.option.true=?
manus.interaction.openBrowser.option.false=?

# ManusProperties - ?????
manus.agent.maxSteps.description=?????????
manus.agent.forceOverrideFromYaml.description=????YAML????????Agent
manus.agent.forceOverrideFromYaml.option.true=?
manus.agent.forceOverrideFromYaml.option.false=?
manus.agent.userInputTimeout.description=????????????(?)
manus.agent.maxMemory.description=?????????
manus.agent.parallelToolCalls.description=??????
manus.agent.parallelToolCalls.option.true=?
manus.agent.parallelToolCalls.option.false=?

# ManusProperties - ???????
manus.infiniteContext.enabled.description=?????????
manus.infiniteContext.enabled.option.true=?
manus.infiniteContext.enabled.option.false=?
manus.infiniteContext.parallelThreads.description=???????
manus.infiniteContext.taskContextSize.description=?????????????(???)

# ManusProperties - ??????
manus.filesystem.allowExternalAccess.description=????????????????
manus.filesystem.allowExternalAccess.option.true=?
manus.filesystem.allowExternalAccess.option.false=?

# ManusProperties - MCP???????
manus.mcpServiceLoader.connectionTimeoutSeconds.description=MCP??????(?)
manus.mcpServiceLoader.maxRetryCount.description=MCP????????
manus.mcpServiceLoader.maxConcurrentConnections.description=MCP???????

# PromptEnum ??
prompt.PLANNING_PLAN_CREATION.description=???????Prompt???????????????
prompt.AGENT_CURRENT_STEP_ENV.description=??????????????agent??????????????????????????????agent??????????
prompt.AGENT_STEP_EXECUTION.description=??agent?????????agent???????????????????????????????????????????agent????prompt
prompt.PLANNING_PLAN_FINALIZER.description=????????prompt????????????????????????????
prompt.DIRECT_RESPONSE.description=?????????prompt???????????????????
prompt.AGENT_STUCK_ERROR.description=Agent????????????
prompt.SUMMARY_PLAN_TEMPLATE.description=?????????JSON??
prompt.MAPREDUCE_TOOL_DESCRIPTION.description=MapReduce?????????
prompt.MAPREDUCE_TOOL_PARAMETERS.description=MapReduce?????????JSON
prompt.AGENT_DEBUG_DETAIL_OUTPUT.description=Agent????????????
prompt.AGENT_NORMAL_OUTPUT.description=Agent??????????
prompt.AGENT_PARALLEL_TOOL_CALLS_RESPONSE.description=Agent???????????
prompt.FORM_INPUT_TOOL_DESCRIPTION.description=???????????
prompt.FORM_INPUT_TOOL_PARAMETERS.description=???????????JSON
