import{d as q,u as T,r as f,c as A,o as I,L as R,a as u,b as c,e,f as m,g as k,t,n as g,w as p,v as y,h as z,i as b,j as h,k as U,T as K,F as P,l as E,m as F,p as O}from"./index-B-dUWZe2.js";import{L as j}from"./llm-check-D2idVWhZ.js";import{_ as B}from"./_plugin-vue_export-helper-DlAUqK2U.js";const H={class:"init-container"},J={class:"init-card"},G={class:"init-header"},Q={class:"description"},W={class:"step-indicator"},X={class:"step-label"},Y={class:"step-label"},Z={key:0,class:"init-form language-selection"},x={class:"form-group"},ee={class:"form-label"},se={class:"language-options"},ae={class:"form-actions single"},le=["disabled"],te={key:1,class:"init-form"},oe={class:"form-group"},ie={class:"form-label"},ne={class:"config-mode-selection"},de={class:"radio-text"},re={class:"radio-text"},ue={key:0,class:"form-group"},ce={for:"apiKey",class:"form-label"},pe=["placeholder","disabled"],me={class:"form-hint"},ve={href:"https://bailian.console.aliyun.com/?tab=model#/api-key",target:"_blank",class:"help-link"},fe={key:1,class:"custom-config-section"},ge={class:"form-group"},be={for:"baseUrl",class:"form-label"},he=["placeholder","disabled"],ye={class:"form-hint"},_e={class:"form-group"},Ne={for:"customApiKey",class:"form-label"},$e=["placeholder","disabled"],Me={class:"form-group"},ke={for:"modelName",class:"form-label"},Ue=["placeholder","disabled"],Ke={class:"form-hint"},Le={class:"form-group"},Se={for:"modelDisplayName",class:"form-label"},we=["placeholder","disabled"],Ce={class:"form-actions"},De=["disabled"],Ve=["disabled"],qe={key:0,class:"loading-spinner"},Te={key:0,class:"error-message"},Ae={key:0,class:"success-message"},Ie={class:"background-animation"},Re=q({__name:"index",setup(ze){const{t:v,locale:N}=T(),_=O(),n=f(1),d=f(N.value||"en"),l=f({configMode:"dashscope",apiKey:"",baseUrl:"",modelName:"",modelDisplayName:""}),i=f(!1),r=f(""),$=f(!1),L=A(()=>l.value.apiKey.trim()?l.value.configMode==="custom"?l.value.baseUrl.trim()&&l.value.modelName.trim():!0:!1),S=async()=>{if(d.value)try{i.value=!0,await F(d.value),n.value=2}catch(s){console.warn("Failed to switch language:",s),n.value=2}finally{i.value=!1}},w=()=>{n.value=1},M=()=>{l.value.apiKey="",l.value.baseUrl="",l.value.modelName="",l.value.modelDisplayName="",r.value=""},C=()=>{if(!l.value.apiKey.trim())return r.value=v("init.apiKeyRequired"),!1;if(l.value.configMode==="custom"){if(!l.value.baseUrl.trim())return r.value=v("init.baseUrlRequired"),!1;if(!l.value.modelName.trim())return r.value=v("init.modelNameRequired"),!1}return!0},D=async()=>{if(C())try{i.value=!0,r.value="";const s={configMode:l.value.configMode,apiKey:l.value.apiKey.trim()};l.value.configMode==="custom"&&(s.baseUrl=l.value.baseUrl.trim(),s.modelName=l.value.modelName.trim(),s.modelDisplayName=l.value.modelDisplayName.trim()||l.value.modelName.trim());const o=await(await fetch("/api/init/save",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(s)})).json();o.success?($.value=!0,localStorage.setItem("hasInitialized","true"),localStorage.setItem("hasVisitedHome","true"),j.clearCache(),o.requiresRestart?setTimeout(()=>{confirm(v("init.restartRequired"))?window.location.reload():_.push("/home")},2e3):setTimeout(()=>{_.push("/home")},2e3)):r.value=o.error||v("init.saveFailed")}catch(s){console.error("Save config failed:",s),r.value=v("init.networkError")}finally{i.value=!1}},V=async()=>{try{const a=await(await fetch("/api/init/status")).json();a.success&&a.initialized&&(localStorage.setItem("hasInitialized","true"),_.push("/home"))}catch(s){console.error("Check init status failed:",s)}};return I(()=>{const s=localStorage.getItem(R);s&&(s==="zh"||s==="en")&&(d.value=s,N.value=s),V()}),(s,a)=>(c(),u("div",H,[e("div",J,[e("div",G,[a[9]||(a[9]=e("div",{class:"logo"},[e("h1",null,"🤖 JManus")],-1)),e("h2",null,t(n.value===1?s.$t("init.welcomeStep"):s.$t("init.welcome")),1),e("p",Q,t(n.value===1?s.$t("init.languageStepDescription"):s.$t("init.description")),1)]),e("div",W,[e("div",{class:g(["step",{active:n.value>=1,completed:n.value>1}])},[a[10]||(a[10]=e("span",{class:"step-number"},"1",-1)),e("span",X,t(s.$t("init.stepLanguage")),1)],2),a[12]||(a[12]=e("div",{class:"step-divider"},null,-1)),e("div",{class:g(["step",{active:n.value>=2,completed:n.value>2}])},[a[11]||(a[11]=e("span",{class:"step-number"},"2",-1)),e("span",Y,t(s.$t("init.stepModel")),1)],2)]),n.value===1?(c(),u("div",Z,[e("div",x,[e("label",ee,t(s.$t("init.selectLanguageLabel")),1),e("div",se,[e("label",{class:g(["language-option",{active:d.value==="zh"}])},[p(e("input",{type:"radio","onUpdate:modelValue":a[0]||(a[0]=o=>d.value=o),value:"zh"},null,512),[[y,d.value]]),a[13]||(a[13]=e("span",{class:"language-content"},[e("span",{class:"language-flag"},"🇨🇳"),e("span",{class:"language-text"},[e("strong",null,"中文"),e("small",null,"简体中文")])],-1))],2),e("label",{class:g(["language-option",{active:d.value==="en"}])},[p(e("input",{type:"radio","onUpdate:modelValue":a[1]||(a[1]=o=>d.value=o),value:"en"},null,512),[[y,d.value]]),a[14]||(a[14]=e("span",{class:"language-content"},[e("span",{class:"language-flag"},"🇺🇸"),e("span",{class:"language-text"},[e("strong",null,"English"),e("small",null,"English (US)")])],-1))],2)])]),e("div",ae,[e("button",{type:"button",class:"submit-btn",disabled:!d.value,onClick:S},t(s.$t("init.continueToModel")),9,le)])])):m("",!0),n.value===2?(c(),u("div",te,[e("form",{onSubmit:z(D,["prevent"])},[e("div",oe,[e("label",ie,t(s.$t("init.configModeLabel")),1),e("div",ne,[e("label",{class:g(["radio-option",{active:l.value.configMode==="dashscope"}])},[p(e("input",{type:"radio","onUpdate:modelValue":a[2]||(a[2]=o=>l.value.configMode=o),value:"dashscope",onChange:M},null,544),[[y,l.value.configMode]]),e("span",de,[e("strong",null,t(s.$t("init.dashscopeMode")),1),e("small",null,t(s.$t("init.dashscopeModeDesc")),1)])],2),e("label",{class:g(["radio-option",{active:l.value.configMode==="custom"}])},[p(e("input",{type:"radio","onUpdate:modelValue":a[3]||(a[3]=o=>l.value.configMode=o),value:"custom",onChange:M},null,544),[[y,l.value.configMode]]),e("span",re,[e("strong",null,t(s.$t("init.customMode")),1),e("small",null,t(s.$t("init.customModeDesc")),1)])],2)])]),l.value.configMode==="dashscope"?(c(),u("div",ue,[e("label",ce,[b(t(s.$t("init.apiKeyLabel"))+" ",1),a[15]||(a[15]=e("span",{class:"required"},"*",-1))]),p(e("input",{id:"apiKey","onUpdate:modelValue":a[4]||(a[4]=o=>l.value.apiKey=o),type:"password",class:"form-input",placeholder:s.$t("init.apiKeyPlaceholder"),disabled:i.value,required:""},null,8,pe),[[h,l.value.apiKey]]),e("div",me,[b(t(s.$t("init.apiKeyHint"))+" ",1),e("a",ve,t(s.$t("init.getApiKey")),1)])])):m("",!0),l.value.configMode==="custom"?(c(),u("div",fe,[e("div",ge,[e("label",be,[b(t(s.$t("init.baseUrlLabel"))+" ",1),a[16]||(a[16]=e("span",{class:"required"},"*",-1))]),p(e("input",{id:"baseUrl","onUpdate:modelValue":a[5]||(a[5]=o=>l.value.baseUrl=o),type:"url",class:"form-input",placeholder:s.$t("init.baseUrlPlaceholder"),disabled:i.value,required:""},null,8,he),[[h,l.value.baseUrl]]),e("div",ye,t(s.$t("init.baseUrlHint")),1)]),e("div",_e,[e("label",Ne,[b(t(s.$t("init.customApiKeyLabel"))+" ",1),a[17]||(a[17]=e("span",{class:"required"},"*",-1))]),p(e("input",{id:"customApiKey","onUpdate:modelValue":a[6]||(a[6]=o=>l.value.apiKey=o),type:"password",class:"form-input",placeholder:s.$t("init.customApiKeyPlaceholder"),disabled:i.value,required:""},null,8,$e),[[h,l.value.apiKey]])]),e("div",Me,[e("label",ke,[b(t(s.$t("init.modelNameLabel"))+" ",1),a[18]||(a[18]=e("span",{class:"required"},"*",-1))]),p(e("input",{id:"modelName","onUpdate:modelValue":a[7]||(a[7]=o=>l.value.modelName=o),type:"text",class:"form-input",placeholder:s.$t("init.modelNamePlaceholder"),disabled:i.value,required:""},null,8,Ue),[[h,l.value.modelName]]),e("div",Ke,t(s.$t("init.modelNameHint")),1)]),e("div",Le,[e("label",Se,t(s.$t("init.modelDisplayNameLabel")),1),p(e("input",{id:"modelDisplayName","onUpdate:modelValue":a[8]||(a[8]=o=>l.value.modelDisplayName=o),type:"text",class:"form-input",placeholder:s.$t("init.modelDisplayNamePlaceholder"),disabled:i.value},null,8,we),[[h,l.value.modelDisplayName]])])])):m("",!0),e("div",Ce,[e("button",{type:"button",class:"back-btn",onClick:w,disabled:i.value},t(s.$t("init.back")),9,De),e("button",{type:"submit",class:"submit-btn",disabled:i.value||!L.value},[i.value?(c(),u("span",qe)):m("",!0),b(" "+t(i.value?s.$t("init.saving"):s.$t("init.saveAndContinue")),1)],8,Ve)])],32)])):m("",!0),k(K,{name:"error-fade"},{default:U(()=>[r.value?(c(),u("div",Te,t(r.value),1)):m("",!0)]),_:1}),k(K,{name:"success-fade"},{default:U(()=>[$.value?(c(),u("div",Ae,t(s.$t("init.successMessage")),1)):m("",!0)]),_:1})]),e("div",Ie,[(c(),u(P,null,E(6,o=>e("div",{class:"floating-shape",key:o})),64))]),a[19]||(a[19]=e("div",{class:"background-effects"},[e("div",{class:"gradient-orb orb-1"}),e("div",{class:"gradient-orb orb-2"}),e("div",{class:"gradient-orb orb-3"})],-1))]))}}),Oe=B(Re,[["__scopeId","data-v-4a70901c"]]);export{Oe as default};
