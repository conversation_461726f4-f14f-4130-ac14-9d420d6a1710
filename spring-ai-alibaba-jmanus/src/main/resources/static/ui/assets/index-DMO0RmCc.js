import{d as I,a as u,b as m,q as $,s as L,f as C,x as E,e as t,t as i,r as x,u as j,c as y,o as F,g as f,w as M,j as R,F as V,l as A,p as G,y as H}from"./index-B-dUWZe2.js";import{_ as K}from"./Java-AI-BYpq8IxI.js";import{I as N}from"./iconify-BDg1LCM7.js";import{_ as P}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{L as O}from"./index-C_qcY8JP.js";import{u as W,s as r}from"./sidebar-DDf1Zcjj.js";import"./llm-check-D2idVWhZ.js";const X={key:1,class:"blur-card-content"},z=I({__name:"index",props:{content:{},wrapperStyle:{}},emits:["clickCard"],setup(S,{emit:d}){const n=S,l=d,c=()=>{console.log("[BlurCard] handleClick called with content:",n.content),l("clickCard",n.content),console.log("[BlurCard] clickCard event emitted")};return(a,w)=>{var p,h,g,v,_;return m(),u("button",{class:"blur-card",onClick:c,style:$(a.wrapperStyle)},[(p=a.content)!=null&&p.icon?(m(),L(E(N),{key:0,icon:a.content.icon,class:"blur-card-icon"},null,8,["icon"])):C("",!0),(h=a.content)!=null&&h.title||(g=a.content)!=null&&g.description?(m(),u("div",X,[t("h3",null,i((v=a.content)==null?void 0:v.title),1),t("p",null,i((_=a.content)==null?void 0:_.description),1)])):C("",!0)],4)}}}),U=P(z,[["__scopeId","data-v-48da0039"]]),Q={class:"home-page"},Y={class:"welcome-container"},Z={class:"header"},ee={class:"header-top"},oe={class:"logo-container"},te={class:"tagline"},ae={class:"main-content"},se={class:"conversation-container"},ne={class:"welcome-section"},le={class:"welcome-title"},re={class:"welcome-subtitle"},ce={class:"input-section"},ie={class:"input-container"},de=["placeholder"],me=["disabled"],pe={class:"examples-section"},ue={class:"examples-grid"},he={class:"card-type"},ge=I({__name:"index",setup(S){const d=G(),n=W(),l=x(""),c=x(),{t:a}=j(),w=()=>{const o=Date.now().toString();d.push({name:"direct",params:{id:o}}).then(()=>{console.log("[Home] jump to direct page"+a("common.success"))}).catch(e=>{console.error("[Home] jump to direct page"+a("common.error"),e)})},p=y(()=>[{title:a("home.examples.stockPrice.title"),type:"message",description:a("home.examples.stockPrice.description"),icon:"carbon:chart-line-data",prompt:a("home.examples.stockPrice.prompt")},{title:a("home.examples.weather.title"),type:"message",description:a("home.examples.weather.description"),icon:"carbon:partly-cloudy",prompt:a("home.examples.weather.prompt")}]),h=y(()=>[{title:a("home.examples.queryplan.title"),type:"plan-act",description:a("home.examples.queryplan.description"),icon:"carbon:plan",prompt:a("home.examples.queryplan.prompt"),planJson:{planType:"simple",title:"查询 沈询 阿里的所有信息（用于展示无限上下文能力）",steps:[{stepRequirement:"[BROWSER_AGENT] 通过 百度 查询 沈询 阿里 ， 获取第一页的html 百度数据，合并聚拢 到 html_data 的目录里",terminateColumns:"存放的目录路径"},{stepRequirement:"[BROWSER_AGENT] 从 html_data 目录中找到所有的有效关于沈询 阿里 的网页链接，输出到 link.md里面",terminateColumns:"url地址，说明"}],planId:"planTemplate-1749200517403"}},{title:a("home.examples.ainovel.title"),type:"plan-act",description:a("home.examples.ainovel.description"),icon:"carbon:document-tasks",prompt:a("home.examples.ainovel.prompt"),planJson:{planType:"simple",title:"人工智能逐步击败人类小说创作计划",steps:[{stepRequirement:"[TEXT_FILE_AGENT] 创建小说的大标题和子章节标题的文件,期望是一有10个子章节的的小说，提纲输出到novel.md里，每一个子章节用二级标题，在当前步骤只需要写章节的标题即可,小说的大标题是《人工智能逐步击败人类》",terminateColumns:"文件的名字"},{stepRequirement:"[TEXT_FILE_AGENT] 从novel.md文件获取子标题信息，然后依次完善每一个章节的具体内容，每个轮次只完善一个子章节的内容，用replace来更新内容，每个章节要求有3000字的内容，不要每更新一个章节就查询一下文档的全部内容",terminateColumns:"文件的名字"}],planId:"planTemplate-1753622676988"}}]),g=y(()=>[...p.value,...h.value]),v=o=>{o.type==="message"?D(o):o.type==="plan-act"&&J(o)};F(()=>{console.log("[Home] onMounted called"),console.log("[Home] taskStore:",n),console.log("[Home] examples:",p),n.markHomeVisited(),console.log("[Home] Home visited marked")});const _=async o=>{try{r.createNewTemplate(),r.jsonContent=JSON.stringify(o);const e=await r.saveTemplate();e!=null&&e.duplicate?console.log("[Sidebar] "+a("sidebar.saveCompleted",{message:e.message,versionCount:e.versionCount})):e!=null&&e.saved?console.log("[Sidebar] "+a("sidebar.saveSuccess",{message:e.message,versionCount:e.versionCount})):e!=null&&e.message&&console.log("[Sidebar] "+a("sidebar.saveStatus",{message:e.message}))}catch(e){console.error("[Sidebar] Failed to save the plan to the template library:",e),alert(e.message||a("sidebar.saveFailed"))}},B=()=>{H(()=>{c.value&&(c.value.style.height="auto",c.value.style.height=Math.min(c.value.scrollHeight,200)+"px")})},q=o=>{console.log("[Home] handleKeydown called, key:",o.key),o.key==="Enter"&&!o.shiftKey&&(o.preventDefault(),console.log("[Home] Enter key pressed, calling handleSend"),T())},T=()=>{if(console.log("[Home] handleSend called, userInput:",l.value),!l.value.trim()){console.log("[Home] handleSend aborted - empty input");return}const o=l.value.trim();console.log("[Home] Setting task to store:",o),n.setTask(o),console.log("[Home] Task set to store, current task:",n.currentTask);const e=Date.now().toString();console.log("[Home] Navigating to direct page with chatId:",e),d.push({name:"direct",params:{id:e}}).then(()=>{console.log("[Home] Navigation to direct page completed")}).catch(s=>{console.error("[Home] Navigation error:",s)})},D=o=>{console.log("[Home] selectExample called with example:",o),console.log("[Home] Example prompt:",o.prompt),n.setTask(o.prompt),console.log("[Home] Task set to store from example, current task:",n.currentTask);const e=Date.now().toString();console.log("[Home] Navigating to direct page with chatId:",e),d.push({name:"direct",params:{id:e}}).then(()=>{console.log("[Home] Navigation to direct page completed (from example)")}).catch(s=>{console.error("[Home] Navigation error (from example):",s)})},J=async o=>{console.log("[Home] selectPlan called with plan:",o);try{await _(o.planJson),console.log("[Home] Plan saved to templates");const e=Date.now().toString();await d.push({name:"direct",params:{id:e}}),H(async()=>{await new Promise(k=>setTimeout(k,300)),r.isCollapsed?(await r.toggleSidebar(),console.log("[Sidebar] Sidebar toggled")):console.log("[Sidebar] Sidebar is already open"),await r.loadPlanTemplateList(),console.log("[Sidebar] Template list loaded");const s=r.planTemplateList.find(k=>k.id===o.planJson.planId);if(!s){console.error("[Sidebar] Template not found");return}await r.selectTemplate(s),console.log("[Sidebar] Template selected:",s.title);const b=document.querySelector(".execute-btn");b.disabled?console.error("[Sidebar] Execute button not found or disabled"):(console.log("[Sidebar] Triggering execute button click"),b.click())})}catch(e){console.error("[Home] Error in selectPlan:",e)}};return(o,e)=>(m(),u("div",Q,[t("div",Y,[e[2]||(e[2]=t("div",{class:"background-effects"},[t("div",{class:"gradient-orb orb-1"}),t("div",{class:"gradient-orb orb-2"}),t("div",{class:"gradient-orb orb-3"})],-1)),t("header",Z,[t("div",ee,[f(O)]),t("div",oe,[e[1]||(e[1]=t("div",{class:"logo"},[t("img",{src:K,alt:"JManus",class:"java-logo"}),t("h1",null,"JManus")],-1)),t("span",te,i(o.$t("home.tagline")),1)])]),t("main",ae,[t("div",se,[t("div",ne,[t("h2",le,i(o.$t("home.welcomeTitle")),1),t("p",re,i(o.$t("home.welcomeSubtitle")),1),t("button",{class:"direct-button",onClick:w},i(o.$t("home.directButton")),1)]),t("div",ce,[t("div",ie,[M(t("textarea",{"onUpdate:modelValue":e[0]||(e[0]=s=>l.value=s),ref_key:"textareaRef",ref:c,class:"main-input",placeholder:o.$t("home.inputPlaceholder"),onKeydown:q,onInput:B},null,40,de),[[R,l.value]]),t("button",{class:"send-button",disabled:!l.value.trim(),onClick:T},[f(E(N),{icon:"carbon:send-alt"})],8,me)])]),t("div",pe,[t("div",ue,[(m(!0),u(V,null,A(g.value,s=>(m(),u("div",{key:s.title,class:"card-with-type"},[f(U,{content:s,onClickCard:b=>v(s)},null,8,["content","onClickCard"]),t("span",he,i(s.type),1)]))),128))])])])])])]))}}),we=P(ge,[["__scopeId","data-v-8a1c50e1"]]);export{we as default};
