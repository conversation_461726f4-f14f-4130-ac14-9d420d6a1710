import{d as h,r as n,s as _,b as m,g as p,k as g,a as k,f as w,e as T,x as b,t as x,n as C,T as y,C as B,P as $,z as M}from"./index-B-dUWZe2.js";import{I as N}from"./iconify-BDg1LCM7.js";import{_ as V}from"./_plugin-vue_export-helper-DlAUqK2U.js";const z=h({__name:"Toast",setup(e,{expose:s}){const o=n(!1),c=n(""),t=n("success"),l=n("carbon:checkmark"),u=n(3e3),d=(i,r="success",v=3e3)=>{c.value=i,t.value=r,l.value=r==="success"?"carbon:checkmark":"carbon:error",u.value=v,o.value=!0,setTimeout(()=>{o.value=!1},u.value)},f=()=>{o.value=!1};return s({show:d}),(i,r)=>(m(),_(B,{to:"body"},[p(y,{name:"slide"},{default:g(()=>[o.value?(m(),k("div",{key:0,class:C(["toast",`toast--${t.value}`]),onClick:f},[p(b(N),{icon:l.value,class:"toast-icon"},null,8,["icon"]),T("span",null,x(c.value),1)],2)):w("",!0)]),_:1})]))}}),E=V(z,[["__scopeId","data-v-581895ae"]]);let a=null;const S=()=>{if(!a){const e=$(E),s=document.createElement("div");document.body.appendChild(s),a=e.mount(s)}return{success:(e,s)=>{a==null||a.show(e,"success",s)},error:(e,s)=>{a==null||a.show(e,"error",s)}}};function j(){const e=M({show:!1,text:"",type:"success"});return{message:e,showMessage:(o,c="success")=>{console.log(`显示消息: ${o}, 类型: ${c}`),e.text=o,e.type=c,e.show=!0;const t=c==="error"?5e3:3e3;console.log(`消息将在 ${t}ms 后自动隐藏`),setTimeout(()=>{e.show=!1,console.log("消息已隐藏")},t)}}}export{j as a,S as u};
