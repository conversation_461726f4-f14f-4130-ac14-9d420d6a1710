import{_ as n}from"./Java-AI-BYpq8IxI.js";import{d as c,a as i,b as d,e as o,t as r,g as m,i as p,x as l,p as _}from"./index-B-dUWZe2.js";import{I as u}from"./iconify-BDg1LCM7.js";import{_ as f}from"./_plugin-vue_export-helper-DlAUqK2U.js";const b={class:"not-found-page"},g={class:"error-container"},h={class:"error-message"},k=c({__name:"notFound",setup(v){const t=_(),a=()=>{t.push("/home")};return(e,s)=>(d(),i("div",b,[o("div",g,[s[0]||(s[0]=o("div",{class:"error-icon"},[o("img",{src:n,alt:"Java-AI",width:"96",height:"96",class:"java-logo"})],-1)),s[1]||(s[1]=o("h1",{class:"error-code"},"404",-1)),o("p",h,r(e.$t("error.notFound")),1),o("button",{class:"back-button",onClick:a},[m(l(u),{icon:"carbon:arrow-left"}),p(" "+r(e.$t("error.backToHome")),1)])])]))}}),N=f(k,[["__scopeId","data-v-57698550"]]);export{N as default};
