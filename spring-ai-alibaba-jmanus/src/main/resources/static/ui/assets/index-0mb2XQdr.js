var ct=Object.defineProperty;var rt=(w,n,s)=>n in w?ct(w,n,{enumerable:!0,configurable:!0,writable:!0,value:s}):w[n]=s;var he=(w,n,s)=>rt(w,typeof n!="symbol"?n+"":n,s);import{d as Ce,u as Ie,c as _e,o as Se,a as g,b as p,n as te,x as l,e,f as q,t as i,g as b,i as Y,F as ge,l as ve,h as ie,w as de,j as fe,z as at,r as D,y as ne,A as De,s as ue,T as xe,k as Re,B as $e,q as Ue,C as Ne,D as ut,E as dt,p as lt,G as pt}from"./index-B-dUWZe2.js";import{I as P}from"./iconify-BDg1LCM7.js";import{s as m,P as Ae,u as it}from"./sidebar-DDf1Zcjj.js";import{_ as ye}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{L as ht}from"./llm-check-D2idVWhZ.js";import{L as gt}from"./index-C_qcY8JP.js";import{u as mt,a as vt}from"./useMessage-BBito0HT.js";const ft={class:"sidebar-content"},bt={class:"sidebar-content-header"},kt={class:"sidebar-content-title"},_t={class:"tab-switcher"},$t=["disabled"],Pt={key:0,class:"tab-content"},Ct={class:"new-task-section"},St={class:"sidebar-content-list"},yt={key:0,class:"loading-state"},Et={key:1,class:"error-state"},wt={key:2,class:"empty-state"},Tt=["onClick"],It={class:"task-icon"},Dt={class:"task-details"},xt={class:"task-title"},Rt={class:"task-preview"},At={class:"task-time"},Mt={class:"task-actions"},Nt=["title","onClick"],Ut={key:1,class:"tab-content config-tab"},Lt={key:0,class:"config-container"},Vt={class:"template-info-header"},qt={class:"template-info"},Ft={class:"template-id"},Ot={class:"config-section"},Bt={class:"section-header"},Wt={class:"generator-content"},jt=["placeholder"],Ht={class:"generator-actions"},zt=["disabled"],Jt=["disabled"],Gt={class:"config-section"},Xt={class:"section-header"},Kt={class:"section-actions"},Qt=["disabled","title"],Yt=["disabled","title"],Zt=["disabled"],en=["placeholder"],tn={class:"config-section"},nn={class:"section-header"},sn={class:"execution-content"},on={class:"params-input-group"},an={class:"params-help-text"},ln={class:"params-input-container"},cn=["placeholder"],rn=["title"],un={class:"api-url-display"},dn={class:"api-url-label"},pn={class:"api-url"},hn={class:"api-url-display"},gn={class:"api-url-label"},mn=["disabled"],vn=Ce({__name:"index",emits:["planExecutionRequested"],setup(w,{expose:n,emit:s}){const{t:u}=Ie(),T=["currentPlanId","userRequest","rootPlanId"],f=_e({get(){try{if(!m.jsonContent)return"";const h={...JSON.parse(m.jsonContent)};return T.forEach(y=>{delete h[y]}),JSON.stringify(h,null,2)}catch{return m.jsonContent}},set(o){try{if(!o.trim()){m.jsonContent="";return}const h=JSON.parse(o);let y={};try{y=JSON.parse(m.jsonContent||"{}")}catch{}const K={...h};T.forEach(U=>{y[U]!==void 0&&(K[U]=y[U])}),m.jsonContent=JSON.stringify(K)}catch{m.jsonContent=o}}}),_=s,x=async()=>{try{const o=await m.saveTemplate();o!=null&&o.duplicate?alert(u("sidebar.saveCompleted",{message:o.message,versionCount:o.versionCount})):o!=null&&o.saved?alert(u("sidebar.saveSuccess",{message:o.message,versionCount:o.versionCount})):o!=null&&o.message&&alert(u("sidebar.saveStatus",{message:o.message}))}catch(o){console.error("保存计划修改失败:",o),alert(o.message||u("sidebar.saveFailed"))}},F=async()=>{var o;try{await m.generatePlan(),alert(u("sidebar.generateSuccess",{templateId:((o=m.selectedTemplate)==null?void 0:o.id)??u("sidebar.unknown")}))}catch(h){console.error("生成计划失败:",h),alert(u("sidebar.generateFailed")+": "+h.message)}},$=async()=>{try{await m.updatePlan(),alert(u("sidebar.updateSuccess"))}catch(o){console.error("更新计划失败:",o),alert(u("sidebar.updateFailed")+": "+o.message)}},B=async()=>{console.log("[Sidebar] handleExecutePlan called");try{const o=m.preparePlanExecution();if(!o){console.log("[Sidebar] No plan data available, returning");return}console.log("[Sidebar] 触发计划执行请求:",o),console.log("[Sidebar] Emitting planExecutionRequested event"),_("planExecutionRequested",o),console.log("[Sidebar] Event emitted")}catch(o){console.error("执行计划出错:",o),alert(u("sidebar.executeFailed")+": "+o.message)}finally{m.finishPlanExecution()}},X=o=>{if(isNaN(o.getTime()))return console.warn("Invalid date received:",o),u("time.unknown");const y=new Date().getTime()-o.getTime(),K=Math.floor(y/6e4),U=Math.floor(y/36e5),V=Math.floor(y/864e5);return K<1?u("time.now"):K<60?u("time.minuteAgo",{count:K}):U<24?u("time.hourAgo",{count:U}):V<30?u("time.dayAgo",{count:V}):o.toLocaleDateString("zh-CN")},G=(o,h)=>!o||o.length<=h?o:o.substring(0,h)+"...";return Se(()=>{m.loadPlanTemplateList()}),n({loadPlanTemplateList:m.loadPlanTemplateList,toggleSidebar:m.toggleSidebar,currentPlanTemplateId:m.currentPlanTemplateId}),(o,h)=>(p(),g("div",{class:te(["sidebar-wrapper",{"sidebar-wrapper-collapsed":l(m).isCollapsed}])},[e("div",ft,[e("div",bt,[e("div",kt,i(o.$t("sidebar.title")),1)]),e("div",_t,[e("button",{class:te(["tab-button",{active:l(m).currentTab==="list"}]),onClick:h[0]||(h[0]=y=>l(m).switchToTab("list"))},[b(l(P),{icon:"carbon:list",width:"16"}),Y(" "+i(o.$t("sidebar.templateList")),1)],2),e("button",{class:te(["tab-button",{active:l(m).currentTab==="config"}]),onClick:h[1]||(h[1]=y=>l(m).switchToTab("config")),disabled:!l(m).selectedTemplate},[b(l(P),{icon:"carbon:settings",width:"16"}),Y(" "+i(o.$t("sidebar.configuration")),1)],10,$t)]),l(m).currentTab==="list"?(p(),g("div",Pt,[e("div",Ct,[e("button",{class:"new-task-btn",onClick:h[2]||(h[2]=y=>l(m).createNewTemplate())},[b(l(P),{icon:"carbon:add",width:"16"}),Y(" "+i(o.$t("sidebar.newPlan"))+" ",1),h[11]||(h[11]=e("span",{class:"shortcut"},"⌘ K",-1))])]),e("div",St,[l(m).isLoading?(p(),g("div",yt,[b(l(P),{icon:"carbon:circle-dash",width:"20",class:"spinning"}),e("span",null,i(o.$t("sidebar.loading")),1)])):l(m).errorMessage?(p(),g("div",Et,[b(l(P),{icon:"carbon:warning",width:"20"}),e("span",null,i(l(m).errorMessage),1),e("button",{onClick:h[3]||(h[3]=(...y)=>l(m).loadPlanTemplateList&&l(m).loadPlanTemplateList(...y)),class:"retry-btn"},i(o.$t("sidebar.retry")),1)])):l(m).planTemplateList.length===0?(p(),g("div",wt,[b(l(P),{icon:"carbon:document",width:"32"}),e("span",null,i(o.$t("sidebar.noTemplates")),1)])):(p(!0),g(ge,{key:3},ve(l(m).sortedTemplates,y=>(p(),g("div",{key:y.id,class:te(["sidebar-content-list-item",{"sidebar-content-list-item-active":y.id===l(m).currentPlanTemplateId}]),onClick:K=>l(m).selectTemplate(y)},[e("div",It,[b(l(P),{icon:"carbon:document",width:"20"})]),e("div",Dt,[e("div",xt,i(y.title||o.$t("sidebar.unnamedPlan")),1),e("div",Rt,i(G(y.description||o.$t("sidebar.noDescription"),40)),1)]),e("div",At,i(X(l(m).parseDateTime(y.updateTime||y.createTime))),1),e("div",Mt,[e("button",{class:"delete-task-btn",title:o.$t("sidebar.deleteTemplate"),onClick:ie(K=>l(m).deleteTemplate(y),["stop"])},[b(l(P),{icon:"carbon:close",width:"16"})],8,Nt)])],10,Tt))),128))])])):l(m).currentTab==="config"?(p(),g("div",Ut,[l(m).selectedTemplate?(p(),g("div",Lt,[e("div",Vt,[e("div",qt,[e("h3",null,i(l(m).selectedTemplate.title||o.$t("sidebar.unnamedPlan")),1),e("span",Ft,"ID: "+i(l(m).selectedTemplate.id),1)]),e("button",{class:"back-to-list-btn",onClick:h[4]||(h[4]=y=>l(m).switchToTab("list"))},[b(l(P),{icon:"carbon:arrow-left",width:"16"})])]),e("div",Ot,[e("div",Bt,[b(l(P),{icon:"carbon:generate",width:"16"}),e("span",null,i(o.$t("sidebar.planGenerator")),1)]),e("div",Wt,[de(e("textarea",{"onUpdate:modelValue":h[5]||(h[5]=y=>l(m).generatorPrompt=y),class:"prompt-input",placeholder:o.$t("sidebar.generatorPlaceholder"),rows:"3"},null,8,jt),[[fe,l(m).generatorPrompt]]),e("div",Ht,[e("button",{class:"btn btn-primary btn-sm",onClick:F,disabled:l(m).isGenerating||!l(m).generatorPrompt.trim()},[b(l(P),{icon:l(m).isGenerating?"carbon:circle-dash":"carbon:generate",width:"14",class:te({spinning:l(m).isGenerating})},null,8,["icon","class"]),Y(" "+i(l(m).isGenerating?o.$t("sidebar.generating"):o.$t("sidebar.generatePlan")),1)],8,zt),e("button",{class:"btn btn-secondary btn-sm",onClick:$,disabled:l(m).isGenerating||!l(m).generatorPrompt.trim()||!l(m).jsonContent.trim()},[b(l(P),{icon:"carbon:edit",width:"14"}),Y(" "+i(o.$t("sidebar.updatePlan")),1)],8,Jt)])])]),e("div",Gt,[e("div",Xt,[b(l(P),{icon:"carbon:code",width:"16"}),e("span",null,i(o.$t("sidebar.jsonTemplate")),1),e("div",Kt,[e("button",{class:"btn btn-sm",onClick:h[6]||(h[6]=(...y)=>l(m).rollbackVersion&&l(m).rollbackVersion(...y)),disabled:!l(m).canRollback,title:o.$t("sidebar.rollback")},[b(l(P),{icon:"carbon:undo",width:"14"})],8,Qt),e("button",{class:"btn btn-sm",onClick:h[7]||(h[7]=(...y)=>l(m).restoreVersion&&l(m).restoreVersion(...y)),disabled:!l(m).canRestore,title:o.$t("sidebar.restore")},[b(l(P),{icon:"carbon:redo",width:"14"})],8,Yt),e("button",{class:"btn btn-primary btn-sm",onClick:x,disabled:l(m).isGenerating||l(m).isExecuting},[b(l(P),{icon:"carbon:save",width:"14"})],8,Zt)])]),de(e("textarea",{"onUpdate:modelValue":h[8]||(h[8]=y=>f.value=y),class:"json-editor",placeholder:o.$t("sidebar.jsonPlaceholder"),rows:"12"},null,8,en),[[fe,f.value]])]),e("div",tn,[e("div",nn,[b(l(P),{icon:"carbon:play",width:"16"}),e("span",null,i(o.$t("sidebar.executionController")),1)]),e("div",sn,[e("div",on,[e("label",null,i(o.$t("sidebar.executionParams")),1),e("div",an,i(o.$t("sidebar.executionParamsHelp")),1),e("div",ln,[de(e("input",{"onUpdate:modelValue":h[9]||(h[9]=y=>l(m).executionParams=y),class:"params-input",placeholder:o.$t("sidebar.executionParamsPlaceholder")},null,8,cn),[[fe,l(m).executionParams]]),e("button",{class:"clear-params-btn",onClick:h[10]||(h[10]=(...y)=>l(m).clearExecutionParams&&l(m).clearExecutionParams(...y)),title:o.$t("sidebar.clearParams")},[b(l(P),{icon:"carbon:close",width:"12"})],8,rn)])]),e("div",un,[e("span",dn,i(o.$t("sidebar.apiUrl"))+":",1),e("code",pn,i(l(m).computedApiUrl),1)]),e("div",hn,[e("span",gn,i(o.$t("sidebar.statusApiUrl"))+":",1),h[12]||(h[12]=e("code",{class:"api-url"},"/api/executor/details/{planId}",-1))]),e("button",{class:"btn btn-primary execute-btn",onClick:B,disabled:l(m).isExecuting||l(m).isGenerating},[b(l(P),{icon:l(m).isExecuting?"carbon:circle-dash":"carbon:play",width:"16",class:te({spinning:l(m).isExecuting})},null,8,["icon","class"]),Y(" "+i(l(m).isExecuting?o.$t("sidebar.executing"):o.$t("sidebar.executePlan")),1)],8,mn)])])])):q("",!0)])):q("",!0)])],2))}}),fn=ye(vn,[["__scopeId","data-v-6a8987dc"]]);class Ve{static async sendMessage(n){return ht.withLlmCheck(async()=>{const s=await fetch(`${this.BASE_URL}/execute`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({query:n})});if(!s.ok)throw new Error(`API request failed: ${s.status}`);return await s.json()})}}he(Ve,"BASE_URL","/api/executor");class qe{static async getDetails(n){try{const s=await fetch(`${this.BASE_URL}/details/${n}`);if(s.status===404)return null;if(!s.ok){const f=await s.text();throw new Error(`Failed to get detailed information: ${s.status} - ${f}`)}const u=await s.text(),T=JSON.parse(u);return T&&typeof T=="object"&&!T.currentPlanId&&(T.currentPlanId=n),T}catch(s){return console.error("[CommonApiService] Failed to get plan details:",s),{currentPlanId:n,status:"failed",message:s instanceof Error?s.message:"Failed to save, please retry"}}}static async submitFormInput(n,s){const u=await fetch(`${this.BASE_URL}/submit-input/${n}`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(s)});if(!u.ok){let f;try{f=await u.json()}catch{f={message:`Failed to submit form input: ${u.status}`}}throw new Error(f.message||`Failed to submit form input: ${u.status}`)}const T=u.headers.get("content-type");return T&&T.indexOf("application/json")!==-1?await u.json():{success:!0}}static async getAllPrompts(){try{const n=await fetch(this.BASE_URL);return await(await this.handleResponse(n)).json()}catch(n){throw console.error("Failed to get Prompt list:",n),n}}static async handleResponse(n){if(!n.ok)try{const s=await n.json();throw new Error(s.message||`API request failed: ${n.status}`)}catch{throw new Error(`API request failed: ${n.status} ${n.statusText}`)}return n}}he(qe,"BASE_URL","/api/executor");const Pe=class Pe{constructor(){he(this,"POLL_INTERVAL",5e3);he(this,"state",at({activePlanId:null,lastSequenceSize:0,isPolling:!1,pollTimer:null}));he(this,"callbacks",{});he(this,"planExecutionCache",new Map);he(this,"uiStateCache",new Map);console.log("[PlanExecutionManager] Initialized with callback-based event system")}getCachedPlanRecord(n){return this.planExecutionCache.get(n)}getCachedUIState(n){return this.uiStateCache.get(n)}setCachedUIState(n,s){this.uiStateCache.set(n,s),console.log(`[PlanExecutionManager] Cached UI state for rootPlanId: ${n}`)}getAllCachedRecords(){return new Map(this.planExecutionCache)}hasCachedPlanRecord(n){return this.planExecutionCache.has(n)}setCachedPlanRecord(n,s){this.planExecutionCache.set(n,s),console.log(`[PlanExecutionManager] Cached plan execution record for rootPlanId: ${n}`)}clearCachedPlanRecord(n){const s=this.planExecutionCache.delete(n);return s&&console.log(`[PlanExecutionManager] Cleared cached plan execution record for rootPlanId: ${n}`),s}clearAllCachedRecords(){const n=this.planExecutionCache.size,s=this.uiStateCache.size;this.planExecutionCache.clear(),this.uiStateCache.clear(),console.log(`[PlanExecutionManager] Cleared all caches - Plans: ${n}, UI States: ${s}`)}static getInstance(){return Pe.instance||(Pe.instance=new Pe),Pe.instance}getActivePlanId(){return this.state.activePlanId}getState(){return this.state}setEventCallbacks(n){this.callbacks={...this.callbacks,...n},console.log("[PlanExecutionManager] Event callbacks set:",Object.keys(n))}async handleUserMessageSendRequested(n){if(this.validateAndPrepareUIForNewRequest(n))try{if(await this.sendUserMessageAndSetPlanId(n),this.state.activePlanId)this.initiatePlanExecutionSequence(n,this.state.activePlanId);else throw new Error("Failed to get valid plan ID")}catch(s){console.error("[PlanExecutionManager] Failed to send user message:",s);const u=this.state.activePlanId??"error";this.setCachedUIState(u,{enabled:!0}),this.emitChatInputUpdateState(u),this.state.activePlanId=null}}handlePlanExecutionRequested(n,s){console.log("[PlanExecutionManager] Received plan execution request:",{planId:n,query:s}),n?(this.state.activePlanId=n,this.initiatePlanExecutionSequence(s??"执行计划",n)):console.error("[PlanExecutionManager] Invalid plan execution request: missing planId")}handleCachedPlanExecution(n,s){const u=this.getCachedPlanRecord(n);return u!=null&&u.currentPlanId?(console.log(`[PlanExecutionManager] Found cached plan execution record for rootPlanId: ${n}`),this.handlePlanExecutionRequested(u.currentPlanId,s),!0):(console.log(`[PlanExecutionManager] No cached plan execution record found for rootPlanId: ${n}`),!1)}validateAndPrepareUIForNewRequest(n){if(!n)return console.warn("[PlanExecutionManager] Query is empty"),!1;if(this.state.activePlanId)return!1;this.emitChatInputClear();const s=this.state.activePlanId??"ui-state";return this.setCachedUIState(s,{enabled:!1,placeholder:"Processing..."}),this.emitChatInputUpdateState(s),!0}async sendUserMessageAndSetPlanId(n){try{const s=await Ve.sendMessage(n);if(s!=null&&s.planId)return this.state.activePlanId=s.planId,s;if(s!=null&&s.planTemplateId)return this.state.activePlanId=s.planTemplateId,{...s,planId:s.planTemplateId};throw console.error("[PlanExecutionManager] Failed to get planId from response:",s),new Error("Failed to get valid planId from API response")}catch(s){throw console.error("[PlanExecutionManager] API call failed:",s),s}}initiatePlanExecutionSequence(n,s){console.log(`[PlanExecutionManager] Starting plan execution sequence for query: "${n}", planId: ${s}`);const u=s;this.emitDialogRoundStart(u),this.startPolling()}handlePlanCompletion(n){this.emitPlanCompleted(n.rootPlanId??""),this.state.lastSequenceSize=0,this.stopPolling();try{setTimeout(async()=>{if(this.state.activePlanId)try{await Ae.deletePlanTemplate(this.state.activePlanId),console.log(`[PlanExecutionManager] Plan template ${this.state.activePlanId} deleted successfully`)}catch(s){console.log(`Delete plan execution record failed: ${s.message}`)}},5e3)}catch(s){console.log(`Delete plan execution record failed: ${s.message}`)}n.completed&&(this.state.activePlanId=null,this.emitChatInputUpdateState(n.rootPlanId??""))}handlePlanError(n){this.emitPlanError(n.message??""),this.state.lastSequenceSize=0,this.stopPolling();try{setTimeout(async()=>{if(this.state.activePlanId)try{await Ae.deletePlanTemplate(this.state.activePlanId),console.log(`[PlanExecutionManager] Plan template ${this.state.activePlanId} deleted successfully`)}catch(s){console.log(`Delete plan execution record failed: ${s.message}`)}},5e3)}catch(s){console.log(`Delete plan execution record failed: ${s.message}`)}}async pollPlanStatus(){if(this.state.activePlanId){if(this.state.isPolling){console.log("[PlanExecutionManager] Previous polling still in progress, skipping");return}try{this.state.isPolling=!0;const n=await this.getPlanDetails(this.state.activePlanId);if(!n){console.warn("[PlanExecutionManager] No details received from API");return}if(n.status&&n.status==="failed"){this.handlePlanError(n);return}if(n.rootPlanId&&this.setCachedPlanRecord(n.rootPlanId,n),!n.steps||n.steps.length===0){console.log("[PlanExecutionManager] Simple response without steps detected, handling as completed"),this.emitPlanUpdate(n.rootPlanId??""),this.handlePlanCompletion(n);return}this.emitPlanUpdate(n.rootPlanId??""),n.completed&&this.handlePlanCompletion(n)}catch(n){console.error("[PlanExecutionManager] Failed to poll plan status:",n)}finally{this.state.isPolling=!1}}}async getPlanDetails(n){try{const s=await qe.getDetails(n);return s!=null&&s.rootPlanId&&(this.planExecutionCache.set(s.rootPlanId,s),console.log(`[PlanExecutionManager] Cached plan execution record for rootPlanId: ${s.rootPlanId}`)),s}catch(s){return console.error("[PlanExecutionManager] Failed to get plan details:",s),{currentPlanId:n,status:"failed",message:s instanceof Error?s.message:"Failed to get plan"}}}startPolling(){this.state.pollTimer&&clearInterval(this.state.pollTimer),this.state.pollTimer=window.setInterval(()=>{this.pollPlanStatus()},this.POLL_INTERVAL),console.log("[PlanExecutionManager] Started polling")}async pollPlanStatusImmediately(){console.log("[PlanExecutionManager] Polling plan status immediately"),await this.pollPlanStatus()}stopPolling(){this.state.pollTimer&&(clearInterval(this.state.pollTimer),this.state.pollTimer=null),console.log("[PlanExecutionManager] Stopped polling")}cleanup(){this.stopPolling(),this.state.activePlanId=null,this.state.lastSequenceSize=0,this.state.isPolling=!1,this.clearAllCachedRecords()}emitChatInputClear(){this.callbacks.onChatInputClear&&this.callbacks.onChatInputClear()}emitChatInputUpdateState(n){this.callbacks.onChatInputUpdateState&&this.callbacks.onChatInputUpdateState(n)}emitDialogRoundStart(n){this.callbacks.onDialogRoundStart&&this.callbacks.onDialogRoundStart(n)}emitPlanUpdate(n){this.callbacks.onPlanUpdate&&this.callbacks.onPlanUpdate(n)}emitPlanCompleted(n){this.callbacks.onPlanCompleted&&this.callbacks.onPlanCompleted(n)}emitPlanError(n){this.callbacks.onPlanError&&this.callbacks.onPlanError(n)}};he(Pe,"instance",null);let Le=Pe;const oe=Le.getInstance(),bn={class:"right-panel"},kn={class:"preview-header"},_n={class:"preview-tabs"},$n={class:"tab-button active"},Pn={class:"preview-content"},Cn={class:"step-details"},Sn={key:0,class:"step-info-fixed"},yn={key:0,class:"agent-info"},En={class:"info-item"},wn={class:"label"},Tn={class:"value"},In={class:"info-item"},Dn={class:"label"},xn={class:"value"},Rn={class:"info-item"},An={class:"label"},Mn={class:"value"},Nn={class:"info-item"},Un={class:"label"},Ln={class:"value"},Vn={class:"info-item"},qn={class:"label"},Fn={class:"execution-status"},On={class:"status-item"},Bn={class:"status-text"},Wn={key:0},jn={key:0,class:"think-act-steps"},Hn={class:"steps-container"},zn={class:"step-header"},Jn={class:"step-number"},Gn={class:"think-section"},Xn={class:"think-content"},Kn={class:"input"},Qn={class:"label"},Yn={class:"output"},Zn={class:"label"},es={key:0,class:"action-section"},ts={class:"action-content"},ns={class:"tool-info"},ss={class:"label"},os={class:"value"},as={class:"input"},ls={class:"label"},is={class:"output"},cs={class:"label"},rs={key:0,class:"sub-plan-section"},us={class:"sub-plan-content"},ds={class:"sub-plan-header"},ps={class:"sub-plan-info"},hs={class:"value"},gs={key:0,class:"sub-plan-info"},ms={class:"value"},vs={class:"sub-plan-status"},fs={class:"status-text"},bs={key:0,class:"no-steps-message"},ks={key:1,class:"no-execution-message"},_s={class:"step-basic-info"},$s={class:"info-item"},Ps={class:"label"},Cs={class:"value"},Ss={key:0,class:"info-item"},ys={class:"value"},Es={class:"info-item"},ws={class:"no-execution-hint"},Ts={key:2,class:"execution-indicator"},Is={class:"execution-text"},Ds={key:1,class:"no-selection"},xs=["title"],Rs=Ce({__name:"index",setup(w,{expose:n}){const{t:s}=Ie(),u=D(),T=D(),f=D(),_=D(null),x=D(!1),F=D(!0),$=D(!0),B=_e(()=>f.value?f.value.completed?s("rightPanel.status.completed"):f.value.current?s("rightPanel.status.executing"):s("rightPanel.status.waiting"):""),X=C=>{var L;if(console.log(`[RightPanel] updateDisplayedPlanProgress called with rootPlanId: ${C}`),f.value&&_.value){const R=_.value.rootPlanId??T.value;if(R&&R!==C){console.log(`[RightPanel] Plan ID mismatch - skipping update. Current: ${R}, Requested: ${C}`);return}}console.log(`[RightPanel] Plan ID validation passed - proceeding with update for rootPlanId: ${C}`);const S=oe.getCachedPlanRecord(C);if(!S){console.warn(`[RightPanel] Plan data not found for rootPlanId: ${C}`);return}if(S.steps&&S.steps.length>0){const R=S.steps.length,E=(S.currentStepIndex??0)+1;console.log(`[RightPanel] Progress: ${E} / ${R}`)}if(f.value&&T.value&&(T.value===C||((L=_.value)==null?void 0:L.rootPlanId)===C)&&(console.log(`[RightPanel] Refreshing selected step details for plan: ${C}`),_.value)){const E=_.value,A=o(E.planId,E.rootPlanId,E.subPlanId);A?(h(A,E.stepIndex,E.planId,E.isSubPlan),O()):console.warn("[RightPanel] Could not find plan record for refresh:",E)}},G=(C,S,L,R,E)=>{console.log("[RightPanel] Step selected:",{planId:C,stepIndex:S,rootPlanId:L,subPlanId:R,subStepIndex:E});const A=!!(L&&R&&E!==void 0);_.value={planId:C,stepIndex:S,isSubPlan:A,...A&&{rootPlanId:L,subPlanId:R,subStepIndex:E}};const ee=o(C,L,R);if(!ee){console.warn("[RightPanel] Plan data not found:",{planId:C,rootPlanId:L,subPlanId:R}),f.value=null,_.value=null;return}h(ee,S,C,A)},o=(C,S,L)=>{var A;if(!S||!L)return oe.getCachedPlanRecord(C)??null;const R=oe.getCachedPlanRecord(C);if(R)return R;const E=oe.getCachedPlanRecord(S);if(!(E!=null&&E.agentExecutionSequence))return null;for(const ee of E.agentExecutionSequence)if(ee.thinkActSteps){for(const se of ee.thinkActSteps)if(((A=se.subPlanExecutionRecord)==null?void 0:A.currentPlanId)===L)return se.subPlanExecutionRecord}return null},h=(C,S,L,R)=>{var ke,re,v,c,k;if(!C.steps||S>=C.steps.length){f.value=null,_.value=null,console.warn("[RightPanel] Invalid step data:",{planId:L,stepIndex:S,hasSteps:!!C.steps,stepsLength:(ke=C.steps)==null?void 0:ke.length,message:"Invalid step index"});return}T.value=L;const E=C.steps[S],A=(re=C.agentExecutionSequence)==null?void 0:re[S];console.log("[RightPanel] Step data details:",{planId:L,stepIndex:S,step:E,hasAgentExecutionSequence:!!C.agentExecutionSequence,agentExecutionSequenceLength:(v=C.agentExecutionSequence)==null?void 0:v.length,agentExecution:A,hasThinkActSteps:!!(A!=null&&A.thinkActSteps),thinkActStepsLength:(c=A==null?void 0:A.thinkActSteps)==null?void 0:c.length,isSubPlan:R});const ee=(A==null?void 0:A.status)==="FINISHED",se=!ee&&S===C.currentStepIndex&&!C.completed,me={planId:L,index:S,title:typeof E=="string"?E:E.title||E.description||E.name||`${R?"子":""}步骤 ${S+1}`,description:typeof E=="string"?E:E.description||E,completed:ee,current:se};A&&(me.agentExecution=A),f.value=me,console.log("[RightPanel] Step details updated:",{planId:L,stepIndex:S,stepTitle:f.value.title,hasAgentExecution:!!A,hasThinkActSteps:(((k=A==null?void 0:A.thinkActSteps)==null?void 0:k.length)??0)>0,completed:ee,current:se,planCurrentStep:C.currentStepIndex,planCompleted:C.completed,isSubPlan:R}),A!=null&&A.thinkActSteps&&A.thinkActSteps.forEach((I,a)=>{I.subPlanExecutionRecord&&console.log(`[RightPanel] Found sub-plan in thinkActStep ${a}:`,I.subPlanExecutionRecord)}),setTimeout(()=>{U()},100),O()},y=(C,S,L,R)=>{console.log("[RightPanel] Sub plan step selected (delegating to unified handler):",{rootPlanId:C,subPlanId:S,stepIndex:L,subStepIndex:R}),G(S,R,C,S,R)},K=C=>{u.value=C??void 0},U=()=>{if(!u.value)return;const{scrollTop:C,scrollHeight:S,clientHeight:L}=u.value,R=S-C-L<50,E=S>L;F.value=R,x.value=E&&!R,R?$.value=!0:S-C-L>100&&($.value=!1),console.log("[RightPanel] Scroll state check:",{scrollTop:C,scrollHeight:S,clientHeight:L,isAtBottom:R,hasScrollableContent:E,showButton:x.value,shouldAutoScroll:$.value})},V=()=>{u.value&&(u.value.scrollTo({top:u.value.scrollHeight,behavior:"smooth"}),ne(()=>{$.value=!0,U()}))},O=()=>{!$.value||!u.value||ne(()=>{u.value&&(u.value.scrollTop=u.value.scrollHeight,console.log("[RightPanel] Auto scroll to bottom"))})},Q=C=>{if(C===null||typeof C>"u"||C==="")return"N/A";try{const S=typeof C=="object"?C:JSON.parse(C);return JSON.stringify(S,null,2)}catch{return String(C)}},ce=()=>{f.value=null,T.value=void 0,$.value=!0,u.value&&u.value.removeEventListener("scroll",U)},be=()=>{const C=()=>{const S=u.value;return S?(K(S),S.addEventListener("scroll",U),$.value=!0,U(),console.log("[RightPanel] Scroll listener initialized successfully"),!0):(console.log("[RightPanel] Scroll container not found, retrying..."),!1)};ne(()=>{C()||setTimeout(()=>{C()},100)})};return Se(()=>{console.log("[RightPanel] Component mounted"),ne(()=>{be()})}),De(()=>{console.log("[RightPanel] Component unmounting, cleaning up..."),_.value=null,ce()}),n({updateDisplayedPlanProgress:X,handleStepSelected:G,handleSubPlanStepSelected:y}),(C,S)=>{var L,R;return p(),g("div",bn,[e("div",kn,[e("div",_n,[e("button",$n,[b(l(P),{icon:"carbon:events"}),Y(" "+i(l(s)("rightPanel.stepExecutionDetails")),1)])])]),e("div",Pn,[e("div",Cn,[f.value?(p(),g("div",Sn,[e("h3",null,i(f.value.title||f.value.description||l(s)("rightPanel.defaultStepTitle",{number:f.value.index+1})),1),f.value.agentExecution?(p(),g("div",yn,[e("div",En,[e("span",wn,i(l(s)("rightPanel.executingAgent"))+":",1),e("span",Tn,i(f.value.agentExecution.agentName),1)]),e("div",In,[e("span",Dn,i(l(s)("rightPanel.description"))+":",1),e("span",xn,i(f.value.agentExecution.agentDescription||""),1)]),e("div",Rn,[e("span",An,i(l(s)("rightPanel.callingModel"))+":",1),e("span",Mn,i(f.value.agentExecution.modelName),1)]),e("div",Nn,[e("span",Un,i(l(s)("rightPanel.request"))+":",1),e("span",Ln,i(f.value.agentExecution.agentRequest||""),1)]),e("div",Vn,[e("span",qn,i(l(s)("rightPanel.executionResult"))+":",1),e("span",{class:te(["value",{success:f.value.agentExecution.status==="FINISHED"}])},i(f.value.agentExecution.status||l(s)("rightPanel.executing")),3)])])):q("",!0),e("div",Fn,[e("div",On,[f.value.completed?(p(),ue(l(P),{key:0,icon:"carbon:checkmark-filled",class:"status-icon success"})):f.value.current?(p(),ue(l(P),{key:1,icon:"carbon:in-progress",class:"status-icon progress"})):(p(),ue(l(P),{key:2,icon:"carbon:time",class:"status-icon pending"})),e("span",Bn,i(B.value),1)])])])):q("",!0),e("div",{ref_key:"scrollContainer",ref:u,class:"step-details-scroll-container",onScroll:U},[f.value?(p(),g("div",Wn,[(L=f.value.agentExecution)!=null&&L.thinkActSteps&&f.value.agentExecution.thinkActSteps.length>0?(p(),g("div",jn,[e("h4",null,i(l(s)("rightPanel.thinkAndActionSteps")),1),e("div",Hn,[(p(!0),g(ge,null,ve(f.value.agentExecution.thinkActSteps,(E,A)=>(p(),g("div",{key:A,class:"think-act-step"},[e("div",zn,[e("span",Jn,"#"+i(A+1),1),e("span",{class:te(["step-status",E.status])},i(E.status||l(s)("rightPanel.executing")),3)]),e("div",Gn,[e("h5",null,[b(l(P),{icon:"carbon:thinking"}),Y(" "+i(l(s)("rightPanel.thinking")),1)]),e("div",Xn,[e("div",Kn,[e("span",Qn,i(l(s)("rightPanel.input"))+":",1),e("pre",null,i(Q(E.thinkInput)),1)]),e("div",Yn,[e("span",Zn,i(l(s)("rightPanel.output"))+":",1),e("pre",null,i(Q(E.thinkOutput)),1)])])]),E.actionNeeded?(p(),g("div",es,[e("h5",null,[b(l(P),{icon:"carbon:play"}),Y(" "+i(l(s)("rightPanel.action")),1)]),e("div",ts,[(p(!0),g(ge,null,ve(E.actToolInfoList,(ee,se)=>(p(),g("div",{key:se},[e("div",ns,[e("span",ss,i(l(s)("rightPanel.tool"))+":",1),e("span",os,i(ee.name||""),1)]),e("div",as,[e("span",ls,i(l(s)("rightPanel.toolParameters"))+":",1),e("pre",null,i(Q(ee.parameters)),1)]),e("div",is,[e("span",cs,i(l(s)("rightPanel.executionResult"))+":",1),e("pre",null,i(Q(ee.result)),1)])]))),128))]),E.subPlanExecutionRecord?(p(),g("div",rs,[e("h5",null,[b(l(P),{icon:"carbon:tree-view"}),Y(" "+i(l(s)("rightPanel.subPlan")),1)]),e("div",us,[e("div",ds,[e("div",ps,[S[0]||(S[0]=e("span",{class:"label"},"子计划ID:",-1)),e("span",hs,i(E.subPlanExecutionRecord.currentPlanId),1)]),E.subPlanExecutionRecord.title?(p(),g("div",gs,[S[1]||(S[1]=e("span",{class:"label"},"标题:",-1)),e("span",ms,i(E.subPlanExecutionRecord.title),1)])):q("",!0),e("div",vs,[E.subPlanExecutionRecord.completed?(p(),ue(l(P),{key:0,icon:"carbon:checkmark-filled",class:"status-icon success"})):(p(),ue(l(P),{key:1,icon:"carbon:in-progress",class:"status-icon progress"})),e("span",fs,i(E.subPlanExecutionRecord.completed?"已完成":"执行中"),1)])])])])):q("",!0)])):q("",!0)]))),128))]),f.value.agentExecution&&!((R=f.value.agentExecution.thinkActSteps)!=null&&R.length)?(p(),g("div",bs,[e("p",null,i(l(s)("rightPanel.noStepDetails")),1)])):f.value.agentExecution?q("",!0):(p(),g("div",ks,[b(l(P),{icon:"carbon:information",class:"info-icon"}),e("h4",null,i(l(s)("rightPanel.stepInfo")),1),e("div",_s,[e("div",$s,[e("span",Ps,i(l(s)("rightPanel.stepName"))+":",1),e("span",Cs,i(f.value.title||f.value.description||`步骤 ${f.value.index+1}`),1)]),f.value.description?(p(),g("div",Ss,[S[2]||(S[2]=e("span",{class:"label"},"描述:",-1)),e("span",ys,i(f.value.description),1)])):q("",!0),e("div",Es,[S[3]||(S[3]=e("span",{class:"label"},"状态:",-1)),e("span",{class:te(["value",{"status-completed":f.value.completed,"status-current":f.value.current,"status-pending":!f.value.completed&&!f.value.current}])},i(f.value.completed?"已完成":f.value.current?"执行中":"待执行"),3)])]),e("p",ws,i(l(s)("rightPanel.noExecutionInfo")),1)])),f.value.current&&!f.value.completed?(p(),g("div",Ts,[S[4]||(S[4]=e("div",{class:"execution-waves"},[e("div",{class:"wave wave-1"}),e("div",{class:"wave wave-2"}),e("div",{class:"wave wave-3"})],-1)),e("p",Is,[b(l(P),{icon:"carbon:in-progress",class:"rotating-icon"}),Y(" "+i(l(s)("rightPanel.stepExecuting")),1)])])):q("",!0)])):(p(),g("div",Ds,[b(l(P),{icon:"carbon:events",class:"empty-icon"}),e("h3",null,i(l(s)("rightPanel.noStepSelected")),1),e("p",null,i(l(s)("rightPanel.selectStepHint")),1)]))])):q("",!0),b(xe,{name:"scroll-button"},{default:Re(()=>[x.value?(p(),g("button",{key:0,onClick:V,class:"scroll-to-bottom-btn",title:l(s)("rightPanel.scrollToBottom")},[b(l(P),{icon:"carbon:chevron-down"})],8,xs)):q("",!0)]),_:1})],544)])])])}}}),As=ye(Rs,[["__scopeId","data-v-e90596ce"]]);function Ms(){const w=oe,n=_e(()=>w.getActivePlanId()),s=_e(()=>w.getState()),u=_e(()=>s.value.isPolling),T=_e(()=>!!n.value),f=($,B)=>{w.initiatePlanExecutionSequence($,B)},_=()=>{w.stopPolling()},x=()=>{w.startPolling()},F=()=>{w.cleanup()};return De(()=>{F()}),{activePlanId:n,state:s,isPolling:u,hasActivePlan:T,startExecution:f,stopPolling:_,startPolling:x,cleanup:F}}const Ns={class:"chat-container"},Us={class:"message-content"},Ls={key:0,class:"user-message"},Vs={key:1,class:"assistant-message"},qs={key:0,class:"thinking-section"},Fs={class:"thinking-header"},Os={class:"thinking-avatar"},Bs={class:"thinking-label"},Ws={class:"thinking-content"},js={key:0,class:"thinking"},Hs={key:1,class:"progress"},zs={class:"progress-bar"},Js={class:"progress-text"},Gs={key:2,class:"steps-container"},Xs={class:"steps-title"},Ks=["onClick"],Qs={class:"section-header"},Ys={class:"step-icon"},Zs={class:"step-title"},eo={key:0,class:"step-status current"},to={key:1,class:"step-status completed"},no={key:2,class:"step-status pending"},so={key:0,class:"action-info"},oo={class:"action-description"},ao={class:"action-icon"},lo={key:0,class:"tool-params"},io={class:"param-label"},co={class:"param-content"},ro={key:1,class:"think-details"},uo={class:"think-header"},po={class:"think-label"},ho={class:"think-output"},go={class:"think-content"},mo={key:1,class:"sub-plan-steps"},vo={class:"sub-plan-header"},fo={class:"sub-plan-step-list"},bo=["onClick"],ko={class:"sub-step-indicator"},_o={class:"sub-step-icon"},$o={class:"sub-step-number"},Po={class:"sub-step-content"},Co={class:"sub-step-title"},So={key:2,class:"user-input-form-container"},yo={class:"user-input-message"},Eo={key:0,class:"form-description"},wo=["onSubmit"],To=["for"],Io=["id","name","onUpdate:modelValue"],Do={key:1,class:"form-group"},xo={for:"form-input-genericInput"},Ro=["onUpdate:modelValue"],Ao={type:"submit",class:"submit-user-input-btn"},Mo={key:3,class:"default-processing"},No={class:"processing-indicator"},Uo={class:"response-section"},Lo={class:"response-header"},Vo={class:"response-avatar"},qo={class:"response-name"},Fo={class:"response-content"},Oo={key:0,class:"final-response"},Bo=["innerHTML"],Wo={key:1,class:"response-placeholder"},jo={class:"typing-indicator"},Ho={class:"typing-text"},zo={key:0,class:"message assistant"},Jo={class:"message-content"},Go={class:"assistant-message"},Xo={class:"thinking-section"},Ko={class:"thinking-header"},Qo={class:"thinking-avatar"},Yo={class:"thinking-label"},Zo={class:"thinking-content"},ea={class:"default-processing"},ta={class:"processing-indicator"},na={class:"response-section"},sa={class:"response-header"},oa={class:"response-avatar"},aa={class:"response-name"},la={class:"response-content"},ia={class:"response-placeholder"},ca={class:"typing-indicator"},ra={class:"typing-text"},ua=["title"],da=Ce({__name:"index",props:{mode:{default:"plan"},initialPrompt:{default:""}},emits:["step-selected","sub-plan-step-selected"],setup(w,{expose:n,emit:s}){const u=w,T=s,{t:f}=Ie(),_=Ms(),x=D(),F=D(!1),$=D([]),B=D(),X=D(!1),G=at({}),o=(a,t,r)=>{const d={id:Date.now().toString(),type:a,content:t,timestamp:new Date,...r};return a==="assistant"&&!d.thinking&&!d.content&&(d.thinking=f("chat.thinking")),$.value.push(d),d},h=a=>{const t=$.value[$.value.length-1];t.type==="assistant"&&Object.assign(t,a)},y=async a=>{try{F.value=!0;const t=o("assistant","",{thinking:"正在理解您的请求并准备回复..."}),r=await Ve.sendMessage(a);if(r.planId)console.log("[ChatComponent] Received planId from direct execution:",r.planId),t.planExecution||(t.planExecution={}),t.planExecution.currentPlanId=r.planId,oe.handlePlanExecutionRequested(r.planId,a),delete t.thinking,console.log("[ChatComponent] Started polling for plan execution updates");else{delete t.thinking;const d=K(r,a);t.content=d}}catch(t){console.error("Direct mode error:",t),h({content:U(t)})}finally{F.value=!1}},K=(a,t)=>a.result??a.message??a.content??"",U=a=>{const t=(a==null?void 0:a.message)??(a==null?void 0:a.toString())??"未知错误";return t.includes("网络")||t.includes("network")||t.includes("timeout")?"抱歉，似乎网络连接有些问题。请检查您的网络连接后再试一次，或者稍等几分钟再重新提问。":t.includes("认证")||t.includes("权限")||t.includes("auth")?"抱歉，访问权限出现了问题。这可能是系统配置的问题，请联系管理员或稍后再试。":t.includes("格式")||t.includes("参数")||t.includes("invalid")?"抱歉，您的请求格式可能有些问题。能否请您重新表述一下您的需求？我会尽力理解并帮助您。":`抱歉，处理您的请求时遇到了一些问题（${t}）。请稍后再试，或者换个方式表达您的需求，我会尽力帮助您的。`},V=(a=!1)=>{ne(()=>{if(x.value){const t=x.value;(a||t.scrollHeight-t.scrollTop-t.clientHeight<150)&&t.scrollTo({top:t.scrollHeight,behavior:a?"auto":"smooth"})}})},O=()=>{V(!0),X.value=!1},Q=()=>{if(x.value){const a=x.value,t=a.scrollHeight-a.scrollTop-a.clientHeight<150;X.value=!t&&$.value.length>0}},ce=()=>{x.value&&x.value.addEventListener("scroll",Q)},be=()=>{x.value&&x.value.removeEventListener("scroll",Q)},C=a=>{o("user",a),u.mode==="plan"?console.log("[ChatComponent] Plan mode message sent, parent should handle:",a):y(a)},S=(a,t)=>{var W;const r=((W=a.planExecution)==null?void 0:W.agentExecutionSequence)??[];return t<0||t>=r.length?"IDLE":r[t].status??"IDLE"},L=(a,t)=>{var r,d;if(!((r=a.planExecution)!=null&&r.currentPlanId)){console.warn("[ChatComponent] Cannot handle step click: missing currentPlanId");return}console.log("[ChatComponent] Step clicked:",{planId:a.planExecution.currentPlanId,stepIndex:t,stepTitle:(d=a.planExecution.steps)==null?void 0:d[t]}),T("step-selected",a.planExecution.currentPlanId,t)},R=(a,t)=>{var r;try{const d=(r=a.planExecution)==null?void 0:r.agentExecutionSequence;if(!(d!=null&&d.length))return console.log("[ChatComponent] No agentExecutionSequence found"),[];const W=d[t];if(!W)return console.log(`[ChatComponent] No agentExecution found for step ${t}`),[];if(!W.thinkActSteps)return console.log(`[ChatComponent] No thinkActSteps found for step ${t}`),[];for(const N of W.thinkActSteps)if(N.subPlanExecutionRecord)return console.log(`[ChatComponent] Found sub-plan for step ${t}:`,N.subPlanExecutionRecord),(N.subPlanExecutionRecord.steps??[]).map(j=>typeof j=="string"?j:typeof j=="object"&&j!==null&&(j.title||j.description)||"子步骤");return[]}catch(d){return console.warn("[ChatComponent] Error getting sub-plan steps:",d),[]}},E=(a,t,r)=>{var d;try{const W=(d=a.planExecution)==null?void 0:d.agentExecutionSequence;if(!(W!=null&&W.length))return"pending";const N=W[t];if(!N||!N.thinkActSteps)return"pending";let Z=null;for(const H of N.thinkActSteps)if(H.subPlanExecutionRecord){Z=H.subPlanExecutionRecord;break}if(!Z)return"pending";const j=Z.currentStepIndex;return Z.completed?"completed":j==null?r===0?"current":"pending":r<j?"completed":r===j?"current":"pending"}catch(W){return console.warn("[ChatComponent] Error getting sub-plan step status:",W),"pending"}},A=(a,t,r)=>{var d,W;try{const N=(d=a.planExecution)==null?void 0:d.agentExecutionSequence;if(!(N!=null&&N.length)){console.warn("[ChatComponent] No agentExecutionSequence data for sub-plan step click");return}const Z=N[t];if(!Z){console.warn("[ChatComponent] No agentExecution found for step",t);return}if(!Z.thinkActSteps){console.warn("[ChatComponent] No thinkActSteps found for step",t);return}let j=null;for(const H of Z.thinkActSteps)if(H.subPlanExecutionRecord){j=H.subPlanExecutionRecord;break}if(!(j!=null&&j.currentPlanId)){console.warn("[ChatComponent] No sub-plan data for step click");return}T("sub-plan-step-selected",((W=a.planExecution)==null?void 0:W.currentPlanId)??"",j.currentPlanId,t,r)}catch(N){console.error("[ChatComponent] Error handling sub-plan step click:",N)}},ee=(a,t)=>{var d,W,N,Z;if(!((d=a.planExecution)!=null&&d.steps))return;console.log("[ChatComponent] Starting to update step actions, steps count:",a.planExecution.steps.length,"execution sequence:",((W=t.agentExecutionSequence)==null?void 0:W.length)??0);const r=new Array(a.planExecution.steps.length).fill(null);if((N=t.agentExecutionSequence)!=null&&N.length){const j=Math.min(t.agentExecutionSequence.length,a.planExecution.steps.length);for(let H=0;H<j;H++){const M=t.agentExecutionSequence[H];if((Z=M.thinkActSteps)!=null&&Z.length){const z=M.thinkActSteps[M.thinkActSteps.length-1];z.actionDescription&&z.toolParameters?(r[H]={actionDescription:z.actionDescription,toolParameters:typeof z.toolParameters=="string"?z.toolParameters:JSON.stringify(z.toolParameters,null,2),thinkInput:z.thinkInput??"",thinkOutput:z.thinkOutput??"",status:t.currentStepIndex!==void 0&&H<t.currentStepIndex?"completed":t.currentStepIndex!==void 0&&H===t.currentStepIndex?"current":"pending"},console.log(`[ChatComponent] Step ${H} action set: ${r[H].actionDescription}`)):(r[H]={actionDescription:"思考中",toolParameters:"等待决策",thinkInput:z.thinkInput??"",thinkOutput:z.thinkOutput??"",status:t.currentStepIndex!==void 0&&H===t.currentStepIndex?"current":"pending"},console.log(`[ChatComponent] Step ${H} is thinking`))}else r[H]={actionDescription:t.currentStepIndex!==void 0&&H<t.currentStepIndex?"已完成":"等待中",toolParameters:"无工具参数",thinkInput:"",thinkOutput:"",status:t.currentStepIndex!==void 0&&H<t.currentStepIndex?"completed":"pending"},console.log(`[ChatComponent] 步骤 ${H} 无执行细节, 状态设为: ${r[H].status}`)}}else console.log("[ChatComponent] 没有执行序列数据");a.stepActions=[...r],console.log("[ChatComponent] 步骤动作更新完成:",JSON.stringify(r.map(j=>j==null?void 0:j.actionDescription))),ne(()=>{console.log("[ChatComponent] UI update completed via reactivity")})},se=a=>{console.log("[ChatComponent] Starting dialog round with planId:",a),a&&($.value.findIndex(r=>{var d;return((d=r.planExecution)==null?void 0:d.currentPlanId)===a&&r.type==="assistant"})===-1?(o("assistant","",{planExecution:{currentPlanId:a},thinking:"正在准备执行计划..."}),console.log("[ChatComponent] Created new assistant message for planId:",a)):console.log("[ChatComponent] Found existing assistant message for planId:",a))},me=a=>{var N,Z,j,H;console.log("[ChatComponent] Processing plan update with rootPlanId:",a);const t=oe.getCachedPlanRecord(a);if(!t){console.warn("[ChatComponent] No cached plan data found for rootPlanId:",a);return}if(console.log("[ChatComponent] Retrieved plan details from cache:",t),console.log("[ChatComponent] Plan steps:",t.steps),console.log("[ChatComponent] Plan completed:",t.completed),!t.currentPlanId){console.warn("[ChatComponent] Plan update missing currentPlanId");return}const r=$.value.findIndex(M=>{var z;return((z=M.planExecution)==null?void 0:z.currentPlanId)===t.currentPlanId&&M.type==="assistant"});let d;if(r!==-1)d=$.value[r],console.log("[ChatComponent] Found existing assistant message for currentPlanId:",t.currentPlanId);else{console.warn("[ChatComponent] No existing assistant message found for currentPlanId:",t.currentPlanId),console.log("[ChatComponent] Current messages:",$.value.map(z=>{var ae;return{type:z.type,planId:(ae=z.planExecution)==null?void 0:ae.currentPlanId,content:z.content.substring(0,50)}}));let M=-1;for(let z=$.value.length-1;z>=0;z--)if($.value[z].type==="assistant"){M=z;break}if(M!==-1)d=$.value[M],d.planExecution||(d.planExecution={}),d.planExecution.currentPlanId=t.currentPlanId,console.log("[ChatComponent] Using last assistant message and updating planExecution.currentPlanId to:",t.currentPlanId);else{console.error("[ChatComponent] No assistant message found at all, this should not happen");return}}if(d.planExecution||(d.planExecution={}),d.planExecution=JSON.parse(JSON.stringify(t)),!t.steps||t.steps.length===0){if(console.log("[ChatComponent] Handling simple response without steps"),t.completed){delete d.thinking;const M=t.summary??t.result??t.message??"处理完成";d.content=ke(M),console.log("[ChatComponent] Set simple response content:",d.content)}else t.title&&(d.thinking=`正在执行: ${t.title}`);return}delete d.thinking;const W=t.steps.map(M=>typeof M=="string"?M:typeof M=="object"&&M!==null&&(M.title||M.description)||"步骤");if(d.planExecution&&(d.planExecution.steps=W),t.agentExecutionSequence&&t.agentExecutionSequence.length>0){console.log("[ChatComponent] 发现执行序列数据，数量:",t.agentExecutionSequence.length),ee(d,t);const M=t.currentStepIndex??0;if(M>=0&&M<t.agentExecutionSequence.length){const ae=t.agentExecutionSequence[M].thinkActSteps;if(ae&&ae.length>0){const Ee=ae[ae.length-1];if(Ee.thinkOutput){const Me=Ee.thinkOutput.length>150?Ee.thinkOutput.substring(0,150)+"...":Ee.thinkOutput;d.thinking=`正在思考: ${Me}`}}}}else if(d.planExecution){const M=d.planExecution.currentStepIndex??0,z=(N=d.planExecution.steps)==null?void 0:N[M],ae=typeof z=="string"?z:"";d.thinking=`正在执行: ${ae}`}if(t.userInputWaitState&&d.planExecution?(console.log("[ChatComponent] 需要用户输入:",t.userInputWaitState),d.planExecution.userInputWaitState||(d.planExecution.userInputWaitState={}),d.planExecution.userInputWaitState={message:t.userInputWaitState.message??"",formDescription:t.userInputWaitState.formDescription??"",formInputs:((Z=t.userInputWaitState.formInputs)==null?void 0:Z.map(M=>({label:M.label,value:M.value||""})))??[]},G[j=d.id]??(G[j]={}),d.thinking="等待用户输入..."):(H=d.planExecution)!=null&&H.userInputWaitState&&delete d.planExecution.userInputWaitState,t.completed??t.status==="completed"){console.log("[ChatComponent] Plan is completed, updating final response"),delete d.thinking;let M="";t.summary?M=t.summary:t.result?M=t.result:M="任务已完成",d.content=re(M),console.log("[ChatComponent] Updated completed message:",d.content)}ne(()=>{console.log("[ChatComponent] Plan update UI refresh completed via reactivity")})},ke=a=>a?a.includes("我")||a.includes("您")||a.includes("您好")||a.includes("可以")?a:a.length<10?`${a}！还有什么需要我帮助的吗？`:a.length<50?`好的，${a}。如果您还有其他问题，请随时告诉我。`:`${a}

希望这个回答对您有帮助！还有什么我可以为您做的吗？`:"我明白了，还有什么我可以帮您的吗？",re=a=>a?`${a}`:"任务已完成！还有什么我可以帮您的吗？",v=a=>{console.log("[ChatComponent] Plan completed with rootPlanId:",a);const t=oe.getCachedPlanRecord(a);if(!t){console.warn("[ChatComponent] No cached plan data found for rootPlanId:",a);return}if(console.log("[ChatComponent] Plan details:",t),t.rootPlanId){const r=$.value.findIndex(d=>{var W;return((W=d.planExecution)==null?void 0:W.currentPlanId)===t.rootPlanId});if(r!==-1){const d=$.value[r];delete d.thinking;let N=t.summary??t.result??"任务已完成";!N.includes("我")&&!N.includes("您")&&(N.includes("成功")||N.includes("完成")?N=`很好！${N}。如果您还有其他需要帮助的地方，请随时告诉我。`:N=`我已经完成了您的请求：${N}`),d.content=N,console.log("[ChatComponent] Updated completed message:",d.content)}else console.warn("[ChatComponent] No message found for completed rootPlanId:",t.rootPlanId)}},c=a=>{F.value=!1,$.value[$.value.length-1]={id:Date.now().toString(),type:"assistant",content:a,timestamp:new Date}},k=a=>{if(!a)return"";let t=a.replace(/\n\n/g,"<br><br>").replace(/\n/g,"<br>");return t=t.replace(/(<br><br>)/g,"</p><p>"),t.includes("</p><p>")&&(t=`<p>${t}</p>`),t},I=async a=>{var t;if(!((t=a.planExecution)!=null&&t.currentPlanId)||!a.planExecution.userInputWaitState){console.error("[ChatComponent] 缺少planExecution.currentPlanId或userInputWaitState");return}try{const r={},d=a.planExecution.userInputWaitState.formInputs;d&&d.length>0?Object.entries(G[a.id]).forEach(([N,Z])=>{var M;const j=parseInt(N,10),H=((M=d[j])==null?void 0:M.label)||`input_${N}`;r[H]=Z}):r.genericInput=a.genericInput??"",console.log("[ChatComponent] 提交用户输入:",r);const W=await qe.submitFormInput(a.planExecution.currentPlanId,r);delete a.planExecution.userInputWaitState,delete a.genericInput,delete G[a.id],_.startPolling(),console.log("[ChatComponent] 用户输入提交成功:",W)}catch(r){console.error("[ChatComponent] 用户输入提交失败:",r),alert(`提交失败: ${(r==null?void 0:r.message)||"未知错误"}`)}};return $e(()=>u.initialPrompt,(a,t)=>{console.log("[ChatComponent] initialPrompt changed from:",t,"to:",a),a&&typeof a=="string"&&a.trim()&&a!==t&&(console.log("[ChatComponent] Processing changed initial prompt:",a),ne(()=>{C(a)}))},{immediate:!1}),Se(()=>{console.log("[ChatComponent] Mounted, setting up event listeners"),oe.setEventCallbacks({onPlanUpdate:me,onPlanCompleted:v,onDialogRoundStart:se,onChatInputUpdateState:a=>{console.log("[ChatComponent] Chat input state update for rootPlanId:",a)},onChatInputClear:()=>{console.log("[ChatComponent] Chat input clear requested")},onPlanError:c}),ne(()=>{ce()}),u.initialPrompt&&typeof u.initialPrompt=="string"&&u.initialPrompt.trim()&&(console.log("[ChatComponent] Processing initial prompt:",u.initialPrompt),ne(()=>{C(u.initialPrompt)}))}),De(()=>{console.log("[ChatComponent] Unmounting, cleaning up resources"),be(),B.value&&clearInterval(B.value),_.cleanup(),Object.keys(G).forEach(a=>delete G[a])}),n({handleSendMessage:C,handlePlanUpdate:me,handlePlanCompleted:v,handleDialogRoundStart:se,addMessage:o,handlePlanError:c}),(a,t)=>(p(),g("div",Ns,[e("div",{class:"messages",ref_key:"messagesRef",ref:x},[(p(!0),g(ge,null,ve($.value,r=>{var d,W,N,Z,j,H,M,z,ae;return p(),g("div",{key:r.id,class:te(["message",{user:r.type==="user",assistant:r.type==="assistant"}])},[e("div",Us,[r.type==="user"?(p(),g("div",Ls,i(r.content),1)):(p(),g("div",Vs,[r.thinking||((d=r.planExecution)==null?void 0:d.progress)!==void 0||(((N=(W=r.planExecution)==null?void 0:W.steps)==null?void 0:N.length)??0)>0?(p(),g("div",qs,[e("div",Fs,[e("div",Os,[b(l(P),{icon:"carbon:thinking",class:"thinking-icon"})]),e("div",Bs,i(a.$t("chat.thinkingLabel")),1)]),e("div",Ws,[r.thinking?(p(),g("div",js,[b(l(P),{icon:"carbon:thinking",class:"thinking-icon"}),e("span",null,i(r.thinking),1)])):q("",!0),((Z=r.planExecution)==null?void 0:Z.progress)!==void 0?(p(),g("div",Hs,[e("div",zs,[e("div",{class:"progress-fill",style:Ue({width:r.planExecution.progress+"%"})},null,4)]),e("span",Js,i(r.planExecution.progressText??a.$t("chat.processing")+"..."),1)])):q("",!0),(((H=(j=r.planExecution)==null?void 0:j.steps)==null?void 0:H.length)??0)>0?(p(),g("div",Gs,[e("h4",Xs,i(a.$t("chat.stepExecutionDetails")),1),(p(!0),g(ge,null,ve((M=r.planExecution)==null?void 0:M.steps,(Ee,J)=>{var Me,Fe,Oe,Be,We,je,He,ze,Je,Ge,Xe,Ke,Qe,Ye,Ze,et,tt,nt,st;return p(),g("div",{key:J,class:te(["ai-section",{running:S(r,J)==="RUNNING",completed:S(r,J)==="FINISHED",pending:S(r,J)==="IDLE"}]),onClick:ie(pe=>L(r,J),["stop"])},[e("div",Qs,[e("span",Ys,i(S(r,J)==="FINISHED"?"✓":S(r,J)==="RUNNING"?"▶":"○"),1),e("span",Zs,i(Ee||`${a.$t("chat.step")} ${J+1}`),1),S(r,J)==="RUNNING"?(p(),g("span",eo,i(a.$t("chat.status.executing")),1)):S(r,J)==="FINISHED"?(p(),g("span",to,i(a.$t("chat.status.completed")),1)):(p(),g("span",no,i(a.$t("chat.status.pending")),1))]),r.stepActions&&r.stepActions[J]?(p(),g("div",so,[e("div",oo,[e("span",ao,i(((Me=r.stepActions[J])==null?void 0:Me.status)==="current"?"🔄":((Fe=r.stepActions[J])==null?void 0:Fe.status)==="completed"?"✓":"⏳"),1),e("strong",null,i((Oe=r.stepActions[J])==null?void 0:Oe.actionDescription),1)]),(Be=r.stepActions[J])!=null&&Be.toolParameters?(p(),g("div",lo,[t[0]||(t[0]=e("span",{class:"tool-icon"},"⚙️",-1)),e("span",io,i(a.$t("common.parameters"))+":",1),e("pre",co,i((We=r.stepActions[J])==null?void 0:We.toolParameters),1)])):q("",!0),(je=r.stepActions[J])!=null&&je.thinkOutput?(p(),g("div",ro,[e("div",uo,[t[1]||(t[1]=e("span",{class:"think-icon"},"💭",-1)),e("span",po,i(a.$t("chat.thinkingOutput"))+":",1)]),e("div",ho,[e("pre",go,i((He=r.stepActions[J])==null?void 0:He.thinkOutput),1)])])):q("",!0)])):q("",!0),((ze=R(r,J))==null?void 0:ze.length)>0?(p(),g("div",mo,[e("div",vo,[b(l(P),{icon:"carbon:tree-view",class:"sub-plan-icon"}),t[2]||(t[2]=e("span",{class:"sub-plan-title"},"子执行计划",-1))]),e("div",fo,[(p(!0),g(ge,null,ve(R(r,J),(pe,le)=>(p(),g("div",{key:`sub-${J}-${le}`,class:te(["sub-plan-step-item",{completed:E(r,J,le)==="completed",current:E(r,J,le)==="current",pending:E(r,J,le)==="pending"}]),onClick:ie(ot=>A(r,J,le),["stop"])},[e("div",ko,[e("span",_o,i(E(r,J,le)==="completed"?"✓":E(r,J,le)==="current"?"▶":"○"),1),e("span",$o,i(le+1),1)]),e("div",Po,[e("span",Co,i(pe),1),t[3]||(t[3]=e("span",{class:"sub-step-badge"},"子步骤",-1))])],10,bo))),128))])])):q("",!0),(Je=r.planExecution)!=null&&Je.userInputWaitState&&S(r,J)==="RUNNING"?(p(),g("div",So,[e("p",yo,i(((Xe=(Ge=r.planExecution)==null?void 0:Ge.userInputWaitState)==null?void 0:Xe.message)??a.$t("chat.userInput.message")),1),(Qe=(Ke=r.planExecution)==null?void 0:Ke.userInputWaitState)!=null&&Qe.formDescription?(p(),g("p",Eo,i((Ze=(Ye=r.planExecution)==null?void 0:Ye.userInputWaitState)==null?void 0:Ze.formDescription),1)):q("",!0),e("form",{onSubmit:ie(pe=>I(r),["prevent"]),class:"user-input-form"},[(tt=(et=r.planExecution)==null?void 0:et.userInputWaitState)!=null&&tt.formInputs&&r.planExecution.userInputWaitState.formInputs.length>0?(p(!0),g(ge,{key:0},ve((st=(nt=r.planExecution)==null?void 0:nt.userInputWaitState)==null?void 0:st.formInputs,(pe,le)=>(p(),g("div",{key:le,class:"form-group"},[e("label",{for:`form-input-${pe.label.replace(/\W+/g,"_")}`},i(pe.label)+": ",9,To),de(e("input",{type:"text",id:`form-input-${pe.label.replace(/\W+/g,"_")}`,name:pe.label,"onUpdate:modelValue":ot=>G[r.id][le]=ot,class:"form-input"},null,8,Io),[[fe,G[r.id][le]]])]))),128)):(p(),g("div",Do,[e("label",xo,i(a.$t("common.input"))+":",1),de(e("input",{type:"text",id:"form-input-genericInput",name:"genericInput","onUpdate:modelValue":pe=>r.genericInput=pe,class:"form-input"},null,8,Ro),[[fe,r.genericInput]])])),e("button",Ao,i(a.$t("chat.userInput.submit")),1)],40,wo)])):q("",!0)],10,Ks)}),128))])):!r.content&&(r.thinking||((z=r.planExecution)==null?void 0:z.progress)!==void 0&&(((ae=r.planExecution)==null?void 0:ae.progress)??0)<100)?(p(),g("div",Mo,[e("div",No,[t[4]||(t[4]=e("div",{class:"thinking-dots"},[e("span"),e("span"),e("span")],-1)),e("span",null,i(r.thinking??a.$t("chat.thinkingProcessing")),1)])])):q("",!0)])])):q("",!0),e("div",Uo,[e("div",Lo,[e("div",Vo,[b(l(P),{icon:"carbon:bot",class:"bot-icon"})]),e("div",qo,i(a.$t("chat.botName")),1)]),e("div",Fo,[r.content?(p(),g("div",Oo,[e("div",{class:"response-text",innerHTML:k(r.content)},null,8,Bo)])):(p(),g("div",Wo,[e("div",jo,[t[5]||(t[5]=e("div",{class:"typing-dots"},[e("span"),e("span"),e("span")],-1)),e("span",Ho,i(a.$t("chat.thinkingResponse")),1)])]))])])]))])],2)}),128)),F.value?(p(),g("div",zo,[e("div",Jo,[e("div",Go,[e("div",Xo,[e("div",Ko,[e("div",Qo,[b(l(P),{icon:"carbon:thinking",class:"thinking-icon"})]),e("div",Yo,i(a.$t("chat.thinkingLabel")),1)]),e("div",Zo,[e("div",ea,[e("div",ta,[t[6]||(t[6]=e("div",{class:"thinking-dots"},[e("span"),e("span"),e("span")],-1)),e("span",null,i(a.$t("chat.thinking")),1)])])])]),e("div",na,[e("div",sa,[e("div",oa,[b(l(P),{icon:"carbon:bot",class:"bot-icon"})]),e("div",aa,i(a.$t("chat.botName")),1)]),e("div",la,[e("div",ia,[e("div",ca,[t[7]||(t[7]=e("div",{class:"typing-dots"},[e("span"),e("span"),e("span")],-1)),e("span",ra,i(a.$t("chat.thinkingResponse")),1)])])])])])])])):q("",!0)],512),X.value?(p(),g("div",{key:0,class:"scroll-to-bottom-btn",onClick:O,title:a.$t("chat.scrollToBottom")},[b(l(P),{icon:"carbon:chevron-down"})],8,ua)):q("",!0)]))}}),pa=ye(da,[["__scopeId","data-v-46f87864"]]),ha={class:"input-area"},ga={class:"input-container"},ma={class:"attach-btn",title:"附加文件"},va=["placeholder","disabled"],fa=["title"],ba=["disabled","title"],ka=Ce({__name:"index",props:{placeholder:{default:""},disabled:{type:Boolean,default:!1},initialValue:{default:""}},emits:["send","clear","update-state","plan-mode-clicked"],setup(w,{expose:n,emit:s}){const{t:u}=Ie(),T=w,f=s,_=D(),x=D(""),F=_e(()=>T.placeholder||u("input.placeholder")),$=D(F.value),B=_e(()=>!!T.disabled),X=()=>{ne(()=>{_.value&&(_.value.style.height="auto",_.value.style.height=Math.min(_.value.scrollHeight,120)+"px")})},G=O=>{O.key==="Enter"&&!O.shiftKey&&(O.preventDefault(),o())},o=()=>{if(!x.value.trim()||B.value)return;const O=x.value.trim();f("send",O),y()},h=()=>{f("plan-mode-clicked")},y=()=>{x.value="",X(),f("clear")},K=(O,Q)=>{Q&&($.value=O?Q:u("input.waiting")),f("update-state",O,Q)},U=O=>{x.value=O,X()},V=()=>x.value.trim();return $e(()=>T.initialValue,O=>{O&&O.trim()&&(x.value=O,X())},{immediate:!0}),n({clearInput:y,updateState:K,setInputValue:U,getQuery:V,focus:()=>{var O;return(O=_.value)==null?void 0:O.focus()}}),Se(()=>{}),De(()=>{}),(O,Q)=>(p(),g("div",ha,[e("div",ga,[e("button",ma,[b(l(P),{icon:"carbon:attachment"})]),de(e("textarea",{"onUpdate:modelValue":Q[0]||(Q[0]=ce=>x.value=ce),ref_key:"inputRef",ref:_,class:"chat-input",placeholder:$.value,disabled:B.value,onKeydown:G,onInput:X},null,40,va),[[fe,x.value]]),e("button",{class:"plan-mode-btn",title:O.$t("input.planMode"),onClick:h},[b(l(P),{icon:"carbon:document"}),Y(" "+i(O.$t("input.planMode")),1)],8,fa),e("button",{class:"send-button",disabled:!x.value.trim()||B.value,onClick:o,title:O.$t("input.send")},[b(l(P),{icon:"carbon:send-alt"}),Y(" "+i(O.$t("input.send")),1)],8,ba)])]))}}),_a=ye(ka,[["__scopeId","data-v-639c8b2a"]]);class we{static async getAllCronTasks(){try{const n=await fetch(this.BASE_URL);return await(await this.handleResponse(n)).json()}catch(n){throw console.error("Failed to get cron tasks:",n),n}}static async getCronTaskById(n){try{const s=await fetch(`${this.BASE_URL}/${n}`);return await(await this.handleResponse(s)).json()}catch(s){throw console.error("Failed to get cron task by id:",s),s}}static async createCronTask(n){try{const s=await fetch(this.BASE_URL,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(n)});return await(await this.handleResponse(s)).json()}catch(s){throw console.error("Failed to create cron task:",s),s}}static async updateCronTask(n,s){try{const u=await fetch(`${this.BASE_URL}/${n}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(s)});return await(await this.handleResponse(u)).json()}catch(u){throw console.error("Failed to update cron task:",u),u}}static async updateTaskStatus(n,s){try{const u=await fetch(`${this.BASE_URL}/${n}/status?status=${s}`,{method:"PUT"});await this.handleResponse(u)}catch(u){throw console.error("Failed to update task status:",u),u}}static async deleteCronTask(n){try{const s=await fetch(`${this.BASE_URL}/${n}`,{method:"DELETE"});await this.handleResponse(s)}catch(s){throw console.error("Failed to delete cron task:",s),s}}static async handleResponse(n){if(!n.ok)try{const s=await n.json();throw new Error(s.message||`API request failed: ${n.status}`)}catch{throw new Error(`API request failed: ${n.status} ${n.statusText}`)}return n}}he(we,"BASE_URL","/api/cron-tasks");const Te={validateCronExpression(w){const n=w.trim().split(/\s+/);return n.length>=5&&n.length<=6},formatTime(w){return new Date(w).toLocaleString()},async saveTask(w){try{let n;return w.id?n=await we.updateCronTask(Number(w.id),w):n=await we.createCronTask(w),n}catch(n){throw console.error("Failed to save cron task:",n),n}},async deleteTask(w){try{await we.deleteCronTask(String(w))}catch(n){throw console.error("Failed to delete cron task:",n),n}},async toggleTaskStatus(w){if(!w.id)throw new Error("Task ID is required");const n=w.status===0?1:0;return await we.updateCronTask(Number(w.id),{...w,status:n})},prepareTaskExecution(w){return w.planTemplateId?{useTemplate:!0,planData:{title:w.cronName||"定时任务执行",planData:{id:w.planTemplateId,planTemplateId:w.planTemplateId,planId:w.planTemplateId},params:w.executionParams||void 0}}:{useTemplate:!1,taskContent:w.planDesc||w.cronName||""}}},$a={class:"modal-header"},Pa={class:"header-actions"},Ca={class:"status-switch"},Sa={class:"status-label"},ya={class:"toggle-switch"},Ea=["checked"],wa={class:"modal-content"},Ta={class:"form-group"},Ia={class:"form-label"},Da=["placeholder"],xa={class:"form-group"},Ra={class:"form-label"},Aa=["placeholder"],Ma={class:"form-help"},Na={class:"form-group"},Ua={class:"form-label"},La=["placeholder"],Va={class:"form-group"},qa={class:"form-label"},Fa={class:"template-toggle"},Oa={key:0,class:"template-selector"},Ba={value:""},Wa=["value"],ja={class:"form-help"},Ha={key:0,class:"form-group"},za={class:"time-info"},Ja={class:"time-label"},Ga={class:"time-value"},Xa={key:1,class:"form-group"},Ka={class:"time-info"},Qa={class:"time-label"},Ya={class:"time-value"},Za={class:"modal-footer"},el=["disabled"],tl=Ce({__name:"TaskDetailModal",props:{modelValue:{type:Boolean},task:{}},emits:["update:modelValue","save"],setup(w,{emit:n}){const s=w,u=n,T=D(!1),f=D([]),_=D({cronName:"",cronTime:"",planDesc:"",status:1,linkTemplate:!1,templateId:"",planTemplateId:""});Se(async()=>{try{const o=await Ae.getAllPlanTemplates();o&&o.templates&&(f.value=o.templates.map(h=>({id:h.id,name:h.title||"Unnamed Template"})))}catch(o){console.error("Failed to get template list:",o)}});const F=o=>{o.target===o.currentTarget&&u("update:modelValue",!1)},$=()=>{_.value.linkTemplate=!1,_.value.templateId="",_.value.planTemplateId=""},B=()=>_.value.cronName.trim()?_.value.cronTime.trim()?Te.validateCronExpression(_.value.cronTime)?_.value.planDesc.trim()?_.value.linkTemplate&&!_.value.templateId?(alert("Please select a plan template"),!1):!0:(alert("Task description cannot be empty"),!1):(alert("Invalid Cron expression format, should be 5-6 parts separated by spaces"),!1):(alert("Cron expression cannot be empty"),!1):(alert("Task name cannot be empty"),!1),X=o=>Te.formatTime(o),G=async()=>{var o;if(B()){T.value=!0;try{const h={..._.value,...((o=s.task)==null?void 0:o.id)!==void 0&&{id:s.task.id},cronName:_.value.cronName.trim(),cronTime:_.value.cronTime.trim(),planDesc:_.value.planDesc.trim(),status:_.value.status,planTemplateId:_.value.linkTemplate&&_.value.templateId||""};u("save",h)}finally{T.value=!1}}};return $e(()=>s.task,o=>{if(o){const h=o.templateId||o.planTemplateId||"";_.value={cronName:o.cronName||"",cronTime:o.cronTime||"",planDesc:o.planDesc||"",status:o.status??1,linkTemplate:!!h,templateId:h,planTemplateId:h}}else _.value={cronName:"",cronTime:"",planDesc:"",status:1,linkTemplate:!1,templateId:"",planTemplateId:""}},{immediate:!0}),$e(()=>s.modelValue,o=>{o||(_.value={cronName:"",cronTime:"",planDesc:"",status:1,linkTemplate:!1,templateId:"",planTemplateId:""})}),(o,h)=>(p(),ue(Ne,{to:"body"},[b(xe,{name:"modal"},{default:Re(()=>{var y,K,U;return[o.modelValue?(p(),g("div",{key:0,class:"modal-overlay",onClick:F},[e("div",{class:"modal-container",onClick:h[8]||(h[8]=ie(()=>{},["stop"]))},[e("div",$a,[e("h3",null,i(o.$t("cronTask.taskDetail")),1),e("div",Pa,[e("div",Ca,[e("span",Sa,i(o.$t("cronTask.taskStatus")),1),e("label",ya,[e("input",{type:"checkbox",checked:_.value.status===0,onChange:h[0]||(h[0]=V=>_.value.status=_.value.status===0?1:0)},null,40,Ea),h[9]||(h[9]=e("span",{class:"toggle-slider"},null,-1))])]),e("button",{class:"close-btn",onClick:h[1]||(h[1]=V=>o.$emit("update:modelValue",!1))},[b(l(P),{icon:"carbon:close"})])])]),e("div",wa,[e("form",{onSubmit:ie(G,["prevent"]),class:"task-form"},[e("div",Ta,[e("label",Ia,i(o.$t("cronTask.taskName")),1),de(e("input",{"onUpdate:modelValue":h[2]||(h[2]=V=>_.value.cronName=V),type:"text",class:"form-input",placeholder:o.$t("cronTask.taskNamePlaceholder"),required:""},null,8,Da),[[fe,_.value.cronName]])]),e("div",xa,[e("label",Ra,i(o.$t("cronTask.cronExpression")),1),de(e("input",{"onUpdate:modelValue":h[3]||(h[3]=V=>_.value.cronTime=V),type:"text",class:"form-input",placeholder:o.$t("cronTask.cronExpressionPlaceholder"),required:""},null,8,Aa),[[fe,_.value.cronTime]]),e("div",Ma,i(o.$t("cronTask.cronExpressionHelp")),1)]),e("div",Na,[e("label",Ua,i(o.$t("cronTask.taskDescription")),1),de(e("textarea",{"onUpdate:modelValue":h[4]||(h[4]=V=>_.value.planDesc=V),class:"form-textarea",placeholder:o.$t("cronTask.taskDescriptionPlaceholder"),rows:"4",required:""},null,8,La),[[fe,_.value.planDesc]])]),e("div",Va,[e("label",qa,i(o.$t("cronTask.planTemplate")),1),e("div",Fa,[e("button",{type:"button",class:te(["template-btn",_.value.linkTemplate?"active":""]),onClick:h[5]||(h[5]=V=>_.value.linkTemplate=!0)},[b(l(P),{icon:"carbon:checkmark"}),Y(" "+i(o.$t("cronTask.linkTemplate")),1)],2),e("button",{type:"button",class:te(["template-btn",_.value.linkTemplate?"":"active"]),onClick:$},[b(l(P),{icon:"carbon:close"}),Y(" "+i(o.$t("cronTask.noTemplate")),1)],2)]),_.value.linkTemplate?(p(),g("div",Oa,[de(e("select",{"onUpdate:modelValue":h[6]||(h[6]=V=>_.value.templateId=V),class:"form-select"},[e("option",Ba,i(o.$t("cronTask.selectTemplate")),1),(p(!0),g(ge,null,ve(f.value,V=>(p(),g("option",{key:V.id,value:V.id},i(V.name),9,Wa))),128))],512),[[ut,_.value.templateId]]),e("div",ja,i(o.$t("cronTask.templateHelpText")),1)])):q("",!0)]),(y=o.task)!=null&&y.createTime?(p(),g("div",Ha,[e("div",za,[e("span",Ja,i(o.$t("cronTask.createTime"))+":",1),e("span",Ga,i(X(o.task.createTime)),1)])])):q("",!0),(K=o.task)!=null&&K.updateTime?(p(),g("div",Xa,[e("div",Ka,[e("span",Qa,i(o.$t("cronTask.updateTime"))+":",1),e("span",Ya,i(X(o.task.updateTime)),1)])])):q("",!0)],32)]),e("div",Za,[e("button",{type:"button",class:"cancel-btn",onClick:h[7]||(h[7]=V=>o.$emit("update:modelValue",!1))},i(o.$t("common.cancel")),1),e("button",{type:"button",class:"save-btn",onClick:G,disabled:T.value},[T.value?(p(),ue(l(P),{key:0,icon:"carbon:loading",class:"loading-icon"})):q("",!0),Y(" "+i((U=s.task)!=null&&U.id?o.$t("common.save"):o.$t("common.create")),1)],8,el)])])])):q("",!0)]}),_:1})]))}}),nl=ye(tl,[["__scopeId","data-v-c9380237"]]),sl={class:"modal-header"},ol={class:"header-actions"},al={class:"modal-content"},ll={key:0,class:"loading-container"},il={key:1,class:"empty-container"},cl={key:2,class:"task-list"},rl=["onClick"],ul={class:"task-main"},dl={class:"task-info"},pl={class:"task-header"},hl={class:"task-name"},gl={class:"task-description"},ml={class:"task-time"},vl=["onClick"],fl=["onClick","disabled","title"],bl=["onClick","title"],kl={class:"dropdown-menu"},_l=["onClick"],$l=["onClick","disabled"],Pl=["onClick","disabled"],Cl={class:"confirm-header"},Sl={class:"confirm-content"},yl={class:"confirm-actions"},El=["disabled"],wl={class:"confirm-header"},Tl={class:"confirm-content"},Il={class:"create-options"},Dl={class:"option-content"},xl={class:"option-title"},Rl={class:"option-desc"},Al={class:"option-content"},Ml={class:"option-title"},Nl={class:"option-desc"},Ul={class:"confirm-actions"},Ll=Ce({__name:"index",props:{modelValue:{type:Boolean,required:!0}},emits:["update:modelValue"],setup(w,{emit:n}){const s=lt(),u=it(),T=mt(),{t:f}=Ie(),_=w,x=n,F=D([]),$=D(!1),B=D(null),X=D(null),G=D(null),o=D(null),h=D(!1),y=D(null),K=D(!1),U=D(null),V=D(!1),O=c=>{c.target===c.currentTarget&&x("update:modelValue",!1)},Q=async()=>{$.value=!0;try{F.value=await we.getAllCronTasks()}catch(c){console.error("Failed to load cron tasks:",c),T.error(`Failed to load tasks: ${c instanceof Error?c.message:String(c)}`)}finally{$.value=!1}},ce=async c=>{B.value=c;try{const k=F.value.find(t=>t.id===c);if(!k){console.error("Task not found:",c);return}x("update:modelValue",!1);const I=Date.now().toString();await s.push({name:"direct",params:{id:I}}),await new Promise(t=>setTimeout(t,100));const a=Te.prepareTaskExecution(k);a.useTemplate&&a.planData?u.emitPlanExecutionRequested(a.planData):a.taskContent&&u.setTask(a.taskContent)}catch(k){console.error("Failed to execute task:",k),T.error(`Execution failed: ${k instanceof Error?k.message:String(k)}`)}finally{B.value=null}},be=c=>{y.value={...c},h.value=!0,o.value=null},C=async c=>{try{await Te.saveTask(c),await Q(),h.value=!1,T.success("Task saved successfully")}catch(k){console.error("Failed to save task:",k),T.error(`Save failed: ${k instanceof Error?k.message:String(k)}`)}},S=c=>{U.value=c,K.value=!0},L=async()=>{var c;if((c=U.value)!=null&&c.id){X.value=U.value.id;try{await Te.deleteTask(U.value.id),await Q(),K.value=!1,U.value=null,T.success("Task deleted successfully")}catch(k){console.error("Failed to delete task:",k),T.error(`Delete failed: ${k instanceof Error?k.message:String(k)}`)}finally{X.value=null}}},R=()=>{K.value=!1,U.value=null},E=c=>{o.value=o.value===c?null:c},A=async c=>{if(c.id){G.value=c.id;try{await Te.toggleTaskStatus(c),await Q(),o.value=null,T.success(`Task ${c.status===0?"disabled":"enabled"} successfully`)}catch(k){console.error("Failed to toggle task status:",k),T.error(`Status toggle failed: ${k instanceof Error?k.message:String(k)}`)}finally{G.value=null}}},ee=async c=>{try{await navigator.clipboard.writeText(c),T.success("Cron expression copied successfully")}catch(k){T.error(`Copy failed: ${k instanceof Error?k.message:String(k)}`)}},se=()=>{V.value=!0},me=()=>{V.value=!1;try{x("update:modelValue",!1);const c=f("cronTask.template");u.setTaskToInput(c);const k=Date.now().toString();s.push({name:"direct",params:{id:k}})}catch(c){console.error("Error in createWithJmanus:",c),T.error(`Creation failed: ${c instanceof Error?c.message:String(c)}`)}},ke=()=>{V.value=!1,y.value={cronName:"",cronTime:"",planDesc:"",status:0,planTemplateId:""},h.value=!0},re=()=>{V.value=!1},v=c=>{const k=c.target;!k.closest(".action-dropdown")&&!k.closest(".dropdown-menu")&&(o.value=null)};return Se(()=>{document.addEventListener("click",v,!0)}),De(()=>{document.removeEventListener("click",v,!0)}),$e(()=>_.modelValue,c=>{c&&Q()}),(c,k)=>(p(),g(ge,null,[(p(),ue(Ne,{to:"body"},[b(xe,{name:"modal"},{default:Re(()=>[w.modelValue?(p(),g("div",{key:0,class:"modal-overlay",onClick:O},[e("div",{class:"modal-container",onClick:k[3]||(k[3]=ie(()=>{},["stop"]))},[e("div",sl,[e("h3",null,i(c.$t("cronTask.title")),1),e("div",ol,[e("button",{class:"add-task-btn",onClick:[se,k[0]||(k[0]=ie(()=>{},["stop"]))]},[b(l(P),{icon:"carbon:add"}),Y(" "+i(c.$t("cronTask.addTask")),1)]),e("button",{class:"close-btn",onClick:k[1]||(k[1]=I=>c.$emit("update:modelValue",!1))},[b(l(P),{icon:"carbon:close"})])])]),e("div",al,[$.value?(p(),g("div",ll,[b(l(P),{icon:"carbon:loading",class:"loading-icon"}),e("span",null,i(c.$t("common.loading")),1)])):F.value.length===0?(p(),g("div",il,[b(l(P),{icon:"carbon:time",class:"empty-icon"}),e("span",null,i(c.$t("cronTask.noTasks")),1)])):(p(),g("div",cl,[(p(!0),g(ge,null,ve(F.value,I=>(p(),g("div",{key:I.id||"",class:"task-item",onClick:a=>be(I)},[e("div",ul,[e("div",dl,[e("div",pl,[e("div",hl,i(I.cronName),1),e("div",{class:te(["task-status-badge",I.status===0?"active":"inactive"])},[b(l(P),{icon:I.status===0?"carbon:checkmark-filled":"carbon:pause-filled"},null,8,["icon"]),e("span",null,i(I.status===0?c.$t("cronTask.active"):c.$t("cronTask.inactive")),1)],2)]),e("div",gl,i(I.planDesc),1),e("div",ml,[b(l(P),{icon:"carbon:time"}),e("span",{class:"cron-readable",style:{cursor:"pointer"},onClick:ie(a=>ee(I.cronTime),["stop"])},i(I.cronTime),9,vl)])])]),e("div",{class:"task-actions",onClick:k[2]||(k[2]=ie(()=>{},["stop"]))},[e("button",{class:"action-btn execute-btn",onClick:a=>ce(I.id),disabled:B.value===I.id,title:c.$t("cronTask.executeOnce")},[b(l(P),{icon:B.value===I.id?"carbon:loading":"carbon:play-filled"},null,8,["icon"]),Y(" "+i(c.$t("cronTask.executeOnce")),1)],8,fl),e("div",{class:te(["action-dropdown",{active:o.value===I.id}])},[e("button",{class:"action-btn dropdown-btn",onClick:a=>E(I.id),title:c.$t("cronTask.operations")},[b(l(P),{icon:"carbon:overflow-menu-horizontal"}),Y(" "+i(c.$t("cronTask.operations")),1)],8,bl),de(e("div",kl,[e("button",{class:"dropdown-item edit-btn",onClick:a=>be(I)},[b(l(P),{icon:"carbon:edit"}),Y(" "+i(c.$t("cronTask.edit")),1)],8,_l),e("button",{class:"dropdown-item toggle-btn",onClick:a=>A(I),disabled:G.value===I.id},[b(l(P),{icon:G.value===I.id?"carbon:loading":I.status===0?"carbon:pause-filled":"carbon:play-filled"},null,8,["icon"]),Y(" "+i(I.status===0?c.$t("cronTask.disable"):c.$t("cronTask.enable")),1)],8,$l),e("button",{class:"dropdown-item delete-btn",onClick:a=>S(I),disabled:X.value===I.id},[b(l(P),{icon:X.value===I.id?"carbon:loading":"carbon:trash-can"},null,8,["icon"]),Y(" "+i(c.$t("cronTask.delete")),1)],8,Pl)],512),[[dt,o.value===I.id]])],2)])],8,rl))),128))]))])])])):q("",!0)]),_:1})])),b(nl,{modelValue:h.value,"onUpdate:modelValue":k[4]||(k[4]=I=>h.value=I),task:y.value,onSave:C},null,8,["modelValue","task"]),(p(),ue(Ne,{to:"body"},[b(xe,{name:"modal"},{default:Re(()=>{var I,a,t,r;return[K.value?(p(),g("div",{key:0,class:"modal-overlay",onClick:R},[e("div",{class:"confirm-modal",onClick:k[5]||(k[5]=ie(()=>{},["stop"]))},[e("div",Cl,[b(l(P),{icon:"carbon:warning",class:"warning-icon"}),e("h3",null,i(c.$t("cronTask.deleteConfirm")),1)]),e("div",Sl,[e("p",null,i(c.$t("cronTask.deleteConfirmMessage",{taskName:((I=U.value)==null?void 0:I.cronName)||((a=U.value)==null?void 0:a.planDesc)||""})),1)]),e("div",yl,[e("button",{class:"confirm-btn cancel-btn",onClick:R},i(c.$t("common.cancel")),1),e("button",{class:"confirm-btn delete-btn",onClick:L,disabled:X.value===((t=U.value)==null?void 0:t.id)},[b(l(P),{icon:X.value===((r=U.value)==null?void 0:r.id)?"carbon:loading":"carbon:trash-can"},null,8,["icon"]),Y(" "+i(c.$t("cronTask.delete")),1)],8,El)])])])):q("",!0)]}),_:1})])),(p(),ue(Ne,{to:"body"},[b(xe,{name:"modal"},{default:Re(()=>[V.value?(p(),g("div",{key:0,class:"modal-overlay",onClick:re},[e("div",{class:"confirm-modal create-options-modal",onClick:k[6]||(k[6]=ie(()=>{},["stop"]))},[e("div",wl,[b(l(P),{icon:"carbon:time",class:"create-icon"}),e("h3",null,i(c.$t("cronTask.createTask")),1)]),e("div",Tl,[e("p",null,i(c.$t("cronTask.selectCreateMethod")),1),e("div",Il,[e("button",{class:"create-option-btn jmanus-btn",onClick:me},[b(l(P),{icon:"carbon:ai-status"}),e("div",Dl,[e("span",xl,i(c.$t("cronTask.createWithJmanus")),1),e("span",Rl,i(c.$t("cronTask.createWithJmanusDesc")),1)])]),e("button",{class:"create-option-btn manual-btn",onClick:ke},[b(l(P),{icon:"carbon:edit"}),e("div",Al,[e("span",Ml,i(c.$t("cronTask.createManually")),1),e("span",Nl,i(c.$t("cronTask.createManuallyDesc")),1)])])])]),e("div",Ul,[e("button",{class:"confirm-btn cancel-btn",onClick:re},i(c.$t("common.cancel")),1)])])])):q("",!0)]),_:1})]))],64))}}),Vl=ye(Ll,[["__scopeId","data-v-f31a9ce7"]]),ql={class:"direct-page"},Fl={class:"direct-chat"},Ol={class:"chat-header"},Bl={class:"header-actions"},Wl=["title"],jl=["title"],Hl={class:"chat-content"},zl=["title"],Jl={class:"message-content"},Gl=Ce({__name:"index",setup(w){const n=pt(),s=lt(),u=it(),{t:T}=Ie(),{message:f}=vt(),_=D(""),x=D(""),F=D(),$=D(),B=D(),X=D(!1),G=D(!1),o=D(null),h=D(!1),y=D(50),K=D(!1),U=D(0),V=D(0);Se(()=>{if(console.log("[Direct] onMounted called"),console.log("[Direct] taskStore.currentTask:",u.currentTask),console.log("[Direct] taskStore.hasUnprocessedTask():",u.hasUnprocessedTask()),oe.setEventCallbacks({onPlanUpdate:c=>{console.log("[Direct] Plan update event received for rootPlanId:",c),C(c)&&(console.log("[Direct] Processing plan update for current rootPlanId:",c),$.value&&typeof $.value.handlePlanUpdate=="function"?(console.log("[Direct] Calling chatRef.handlePlanUpdate with rootPlanId:",c),$.value.handlePlanUpdate(c)):console.warn("[Direct] chatRef.handlePlanUpdate method not available"),F.value&&typeof F.value.updateDisplayedPlanProgress=="function"?(console.log("[Direct] Calling rightPanelRef.updateDisplayedPlanProgress with rootPlanId:",c),F.value.updateDisplayedPlanProgress(c)):console.warn("[Direct] rightPanelRef.updateDisplayedPlanProgress method not available"))},onPlanCompleted:c=>{if(console.log("[Direct] Plan completed event received for rootPlanId:",c),!!C(c)){if(console.log("[Direct] Processing plan completion for current rootPlanId:",c),$.value&&typeof $.value.handlePlanCompleted=="function"){const k=oe.getCachedPlanRecord(c);console.log("[Direct] Calling chatRef.handlePlanCompleted with details:",k),$.value.handlePlanCompleted(k??{planId:c})}else console.warn("[Direct] chatRef.handlePlanCompleted method not available");o.value=null,console.log("[Direct] Cleared currentRootPlanId after plan completion")}},onDialogRoundStart:c=>{console.log("[Direct] Dialog round start event received for rootPlanId:",c),o.value=c,console.log("[Direct] Set currentRootPlanId to:",c),$.value&&typeof $.value.handleDialogRoundStart=="function"?(console.log("[Direct] Calling chatRef.handleDialogRoundStart with planId:",c),$.value.handleDialogRoundStart(c)):console.warn("[Direct] chatRef.handleDialogRoundStart method not available")},onChatInputClear:()=>{console.log("[Direct] Chat input clear event received"),L()},onChatInputUpdateState:c=>{if(console.log("[Direct] Chat input update state event received for rootPlanId:",c),!C(c,!0))return;const k=oe.getCachedUIState(c);k&&E(k.enabled,k.placeholder)},onPlanError:c=>{$.value.handlePlanError(c)}}),console.log("[Direct] Event callbacks registered to planExecutionManager"),m.loadPlanTemplateList(),u.hasUnprocessedTask()&&u.currentTask){const c=u.currentTask.prompt;console.log("[Direct] Found unprocessed task from store:",c),u.markTaskAsProcessed(),ne(()=>{$.value&&typeof $.value.handleSendMessage=="function"?(console.log("[Direct] Directly executing task via chatRef.handleSendMessage:",c),$.value.handleSendMessage(c)):(console.warn("[Direct] chatRef.handleSendMessage method not available, falling back to prompt"),_.value=c)})}else{const c=u.getAndClearTaskToInput();c?(x.value=c,console.log("[Direct] Setting inputOnlyContent for input only:",x.value)):(_.value=n.query.prompt||"",console.log("[Direct] Received task from URL:",_.value),console.log("[Direct] No unprocessed task in store"))}const v=localStorage.getItem("directPanelWidth");v&&(y.value=parseFloat(v)),console.log("[Direct] Final prompt value:",_.value),x.value&&ne(()=>{B.value&&typeof B.value.setInputValue=="function"&&(B.value.setInputValue(x.value),console.log("[Direct] Set input value:",x.value),x.value="")}),window.addEventListener("plan-execution-requested",c=>{console.log("[DirectView] Received plan-execution-requested event:",c.detail),re(c.detail)})}),$e(()=>u.currentTask,v=>{if(console.log("[Direct] Watch taskStore.currentTask triggered, newTask:",v),v&&!v.processed){const c=v.prompt;u.markTaskAsProcessed(),console.log("[Direct] Received new task from store:",c),ne(()=>{$.value&&typeof $.value.handleSendMessage=="function"?(console.log("[Direct] Directly executing new task via chatRef.handleSendMessage:",c),$.value.handleSendMessage(c)):console.warn("[Direct] chatRef.handleSendMessage method not available for new task")})}else console.log("[Direct] Task is null or already processed, ignoring")},{immediate:!1}),$e(()=>_.value,(v,c)=>{console.log("[Direct] prompt value changed from:",c,"to:",v)},{immediate:!1}),$e(()=>u.taskToInput,v=>{console.log("[Direct] Watch taskStore.taskToInput triggered, newTaskToInput:",v),v&&v.trim()&&(console.log("[Direct] Setting input value from taskToInput:",v),ne(()=>{B.value&&typeof B.value.setInputValue=="function"&&(B.value.setInputValue(v.trim()),console.log("[Direct] Input value set from taskToInput watch:",v.trim()),u.getAndClearTaskToInput())}))},{immediate:!1}),De(()=>{console.log("[Direct] onUnmounted called, cleaning up resources"),o.value=null,oe.cleanup(),document.removeEventListener("mousemove",Q),document.removeEventListener("mouseup",ce),window.removeEventListener("plan-execution-requested",v=>{re(v.detail)})});const O=v=>{K.value=!0,U.value=v.clientX,V.value=y.value,document.addEventListener("mousemove",Q),document.addEventListener("mouseup",ce),document.body.style.cursor="col-resize",document.body.style.userSelect="none",v.preventDefault()},Q=v=>{if(!K.value)return;const c=window.innerWidth,I=(v.clientX-U.value)/c*100;let a=V.value+I;a=Math.max(20,Math.min(80,a)),y.value=a},ce=()=>{K.value=!1,document.removeEventListener("mousemove",Q),document.removeEventListener("mouseup",ce),document.body.style.cursor="",document.body.style.userSelect="",localStorage.setItem("directPanelWidth",y.value.toString())},be=()=>{y.value=50,localStorage.setItem("directPanelWidth","50")},C=(v,c=!1)=>!o.value||v===o.value||c&&(v==="ui-state"||v==="error")?!0:(console.log("[Direct] Ignoring event for non-current rootPlanId:",v,"current:",o.value),!1),S=v=>{console.log("[DirectView] Send message from input:",v),$.value&&typeof $.value.handleSendMessage=="function"?(console.log("[DirectView] Calling chatRef.handleSendMessage:",v),$.value.handleSendMessage(v)):console.warn("[DirectView] chatRef.handleSendMessage method not available")},L=()=>{console.log("[DirectView] Input cleared"),B.value&&typeof B.value.clear=="function"&&B.value.clear()},R=()=>{console.log("[DirectView] Input focused")},E=(v,c)=>{console.log("[DirectView] Input state updated:",v,c),G.value=!v},A=(v,c)=>{console.log("[DirectView] Step selected:",v,c),F.value&&typeof F.value.handleStepSelected=="function"?(console.log("[DirectView] Forwarding step selection to right panel:",v,c),F.value.handleStepSelected(v,c)):console.warn("[DirectView] rightPanelRef.handleStepSelected method not available")},ee=(v,c,k,I)=>{console.log("[DirectView] Sub plan step selected:",{parentPlanId:v,subPlanId:c,stepIndex:k,subStepIndex:I}),F.value&&typeof F.value.handleSubPlanStepSelected=="function"?(console.log("[DirectView] Forwarding sub plan step selection to right panel:",{parentPlanId:v,subPlanId:c,stepIndex:k,subStepIndex:I}),F.value.handleSubPlanStepSelected(v,c,k,I)):console.warn("[DirectView] rightPanelRef.handleSubPlanStepSelected method not available")},se=()=>{console.log("[DirectView] Plan mode button clicked"),m.toggleSidebar(),console.log("[DirectView] Sidebar toggled, isCollapsed:",m.isCollapsed)},me=()=>{s.push("/home")},ke=()=>{s.push("/configs")},re=async v=>{var k,I,a,t;if(console.log("[DirectView] Plan execution requested:",v),X.value){console.log("[DirectView] Plan execution already in progress, ignoring request");return}X.value=!0;let c=!1;$.value&&typeof $.value.addMessage=="function"?(console.log("[DirectView] Calling chatRef.addMessage for plan execution:",v.title),$.value.addMessage("user",v.title),c=!0):console.warn("[DirectView] chatRef.addMessage method not available");try{const r=((k=v.planData)==null?void 0:k.planTemplateId)||((I=v.planData)==null?void 0:I.id)||((a=v.planData)==null?void 0:a.planId);if(!r)throw new Error("没有找到计划模板ID");console.log("[Direct] Executing plan with templateId:",r,"params:",v.params),console.log("[Direct] About to call PlanActApiService.executePlan");let d;if((t=v.params)!=null&&t.trim()?(console.log("[Direct] Calling executePlan with params:",v.params.trim()),d=await Ae.executePlan(r,v.params.trim())):(console.log("[Direct] Calling executePlan without params"),d=await Ae.executePlan(r)),console.log("[Direct] Plan execution API response:",d),d.planId)console.log("[Direct] Got planId from response:",d.planId,"starting plan execution"),o.value=d.planId,console.log("[Direct] Set currentRootPlanId to:",d.planId),console.log("[Direct] Delegating plan execution to planExecutionManager"),oe.handlePlanExecutionRequested(d.planId,v.title);else throw console.error("[Direct] No planId in response:",d),new Error("执行计划失败：未返回有效的计划ID")}catch(r){console.error("[Direct] Plan execution failed:",r),console.error("[Direct] Error details:",{message:r.message,stack:r.stack}),o.value=null,$.value&&typeof $.value.addMessage=="function"?(console.log("[Direct] Adding error messages to chat"),c||$.value.addMessage("user",v.title),$.value.addMessage("assistant",`执行计划失败: ${r.message||"未知错误"}`,{thinking:void 0})):(console.error("[Direct] Chat ref not available, showing alert"),alert(`执行计划失败: ${r.message||"未知错误"}`))}finally{console.log("[Direct] Plan execution finished, resetting isExecutingPlan flag"),X.value=!1}};return(v,c)=>(p(),g("div",ql,[e("div",Fl,[b(fn,{onPlanExecutionRequested:re}),e("div",{class:"left-panel",style:Ue({width:y.value+"%"})},[e("div",Ol,[e("button",{class:"back-button",onClick:me},[b(l(P),{icon:"carbon:arrow-left"})]),e("h2",null,i(v.$t("conversation")),1),e("div",Bl,[b(gt),e("button",{class:"config-button",onClick:ke,title:v.$t("direct.configuration")},[b(l(P),{icon:"carbon:settings-adjust",width:"20"})],8,Wl),e("button",{class:"cron-task-btn",onClick:c[0]||(c[0]=k=>h.value=!0),title:v.$t("cronTask.title")},[b(l(P),{icon:"carbon:alarm",width:"20"})],8,jl)])]),e("div",Hl,[b(pa,{ref_key:"chatRef",ref:$,mode:"direct","initial-prompt":_.value||"",onStepSelected:A,onSubPlanStepSelected:ee},null,8,["initial-prompt"])]),(p(),ue(_a,{key:v.$i18n.locale,ref_key:"inputRef",ref:B,disabled:G.value,placeholder:G.value?l(T)("input.waiting"):l(T)("input.placeholder"),"initial-value":_.value,onSend:S,onClear:L,onFocus:R,onUpdateState:E,onPlanModeClicked:se},null,8,["disabled","placeholder","initial-value"]))],4),e("div",{class:"panel-resizer",onMousedown:O,onDblclick:be,title:v.$t("direct.panelResizeHint")},c[2]||(c[2]=[e("div",{class:"resizer-line"},null,-1)]),40,zl),b(As,{ref_key:"rightPanelRef",ref:F,style:Ue({width:100-y.value+"%"})},null,8,["style"])]),b(Vl,{modelValue:h.value,"onUpdate:modelValue":c[1]||(c[1]=k=>h.value=k)},null,8,["modelValue"]),l(f).show?(p(),g("div",{key:0,class:te(["message-toast",l(f).type])},[e("div",Jl,[e("span",null,i(l(f).text),1)])],2)):q("",!0)]))}}),si=ye(Gl,[["__scopeId","data-v-ea79c7eb"]]);export{si as default};
