# Test configuration for i18n functionality
spring.messages.basename=i18n/messages
spring.messages.encoding=UTF-8
spring.messages.use-code-as-default-message=true

# Disable unnecessary components for testing
spring.ai.openai.api-key=test-key
spring.ai.openai.base-url=http://localhost:8080

# Database configuration for testing
spring.datasource.url=jdbc:h2:mem:testdb
spring.datasource.driver-class-name=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect
spring.jpa.hibernate.ddl-auto=create-drop

# Logging configuration
logging.level.com.alibaba.cloud.ai.example.manus=DEBUG
logging.level.org.springframework.context.support.ResourceBundleMessageSource=DEBUG
