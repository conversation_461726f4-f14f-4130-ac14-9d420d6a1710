/**
 * Copyright 2024 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/* ========================================
   现代简约 + 科技轻盈感设计系统
   ======================================== */

/* CSS 变量定义 */
:root {
  /* 主色系 - 淡蓝科技感 */
  --primary-color: #5F70E1;
  --primary-hover: #3A7AFE;
  --primary-active: #4C63D2;
  --primary-light: #E8EBFF;
  --primary-lighter: #F5F6FF;
  
  /* 辅助色 - 浅紫活泼点缀 */
  --accent-color: #917DFE;
  --accent-hover: #A794FF;
  --accent-light: #F0EEFF;
  
  /* 功能色系 */
  --success-color: #52C41A;
  --success-light: #F6FFED;
  --warning-color: #FAAD14;
  --warning-light: #FFF7E6;
  --error-color: #FF4D4F;
  --error-light: #FFF2F0;
  --info-color: #1890FF;
  --info-light: #E6F7FF;
  
  /* 中性色系 */
  --text-primary: #1F1F1F;
  --text-secondary: #666666;
  --text-tertiary: #999999;
  --text-quaternary: #BFBFBF;
  --text-disabled: #D9D9D9;
  
  /* 背景色系 */
  --bg-primary: #FFFFFF;
  --bg-secondary: #F5F6FA;
  --bg-tertiary: #FAFBFC;
  --bg-layout: #F8F9FA;
  
  /* 边框色系 */
  --border-primary: #EAEAEA;
  --border-secondary: #F0F0F0;
  --border-tertiary: #F5F5F5;
  
  /* 字体系统 */
  --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  --font-family-mono: 'SF Mono', Monaco, Inconsolata, 'Roboto Mono', 'Source Code Pro', Menlo, Consolas, 'Ubuntu Mono', monospace;
  
  /* 字体尺寸 */
  --font-size-xs: 12px;
  --font-size-sm: 14px;
  --font-size-base: 16px;
  --font-size-lg: 18px;
  --font-size-xl: 20px;
  --font-size-2xl: 24px;
  --font-size-3xl: 30px;
  --font-size-4xl: 36px;
  
  /* 字体重量 */
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  
  /* 间距系统 - 基于 4px */
  --space-xs: 4px;
  --space-sm: 8px;
  --space-base: 12px;
  --space-md: 16px;
  --space-lg: 20px;
  --space-xl: 24px;
  --space-2xl: 32px;
  --space-3xl: 48px;
  --space-4xl: 64px;
  
  /* 圆角系统 */
  --radius-xs: 2px;
  --radius-sm: 4px;
  --radius-base: 6px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
  --radius-2xl: 20px;
  --radius-full: 50%;
  
  /* 阴影系统 */
  --shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.03);
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.04);
  --shadow-base: 0 4px 8px rgba(0, 0, 0, 0.06);
  --shadow-md: 0 6px 16px rgba(0, 0, 0, 0.08);
  --shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.12);
  --shadow-xl: 0 12px 32px rgba(0, 0, 0, 0.16);
  
  /* 过渡动画 */
  --transition-fast: 0.15s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-base: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  
  /* Z-index 层级 */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
}

/* 全局重置 */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

body {
  font-family: var(--font-family);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-normal);
  color: var(--text-primary);
  background-color: var(--bg-layout);
  line-height: 1.6;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
  border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb {
  background: var(--border-primary);
  border-radius: var(--radius-sm);
  transition: background-color var(--transition-base);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-tertiary);
}

/* 全局按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  padding: 8px 16px;
  border: 1px solid transparent;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  line-height: 1.4;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  text-decoration: none;
  white-space: nowrap;
  user-select: none;
  outline: none;
  box-sizing: border-box;
}

.btn:focus {
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: none !important;
}

/* 按钮尺寸 */
.btn-sm {
  padding: 6px 12px;
  font-size: 12px;
}

.btn-lg {
  padding: 12px 24px;
  font-size: 16px;
}

/* 主要按钮 */
.btn-primary {
  background: linear-gradient(135deg, #1890ff, #40a9ff);
  color: white;
  border-color: #1890ff;
  box-shadow: 0 2px 4px rgba(24, 144, 255, 0.2);
}

.btn-primary:hover:not(:disabled) {
  background: linear-gradient(135deg, #40a9ff, #69c0ff);
  border-color: #40a9ff;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
  transform: translateY(-1px);
}

.btn-primary:active:not(:disabled) {
  background: linear-gradient(135deg, #096dd9, #1890ff);
  border-color: #096dd9;
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(24, 144, 255, 0.2);
}

/* 次要按钮 */
.btn-secondary {
  background: #ffffff;
  color: #666;
  border-color: #d9d9d9;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);
}

.btn-secondary:hover:not(:disabled) {
  background: #f5f5f5;
  border-color: #bfbfbf;
  color: #333;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transform: translateY(-1px);
}

.btn-secondary:active:not(:disabled) {
  background: #e6e6e6;
  border-color: #999;
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);
}

/* 成功按钮 */
.btn-success {
  background: linear-gradient(135deg, #52c41a, #73d13d);
  color: white;
  border-color: #52c41a;
  box-shadow: 0 2px 4px rgba(82, 196, 26, 0.2);
}

.btn-success:hover:not(:disabled) {
  background: linear-gradient(135deg, #73d13d, #95de64);
  border-color: #73d13d;
  box-shadow: 0 4px 12px rgba(82, 196, 26, 0.3);
  transform: translateY(-1px);
}

/* 危险按钮 */
.btn-danger {
  background: linear-gradient(135deg, #ff4d4f, #ff7875);
  color: white;
  border-color: #ff4d4f;
  box-shadow: 0 2px 4px rgba(255, 77, 79, 0.2);
}

.btn-danger:hover:not(:disabled) {
  background: linear-gradient(135deg, #ff7875, #ffa39e);
  border-color: #ff7875;
  box-shadow: 0 4px 12px rgba(255, 77, 79, 0.3);
  transform: translateY(-1px);
}

/* 警告按钮 */
.btn-warning {
  background: linear-gradient(135deg, #fa8c16, #ffa940);
  color: white;
  border-color: #fa8c16;
  box-shadow: 0 2px 4px rgba(250, 140, 22, 0.2);
}

.btn-warning:hover:not(:disabled) {
  background: linear-gradient(135deg, #ffa940, #ffc069);
  border-color: #ffa940;
  box-shadow: 0 4px 12px rgba(250, 140, 22, 0.3);
  transform: translateY(-1px);
}

/* 轮廓按钮 */
.btn-outline {
  background: transparent;
  color: #1890ff;
  border-color: #1890ff;
}

.btn-outline:hover:not(:disabled) {
  background: #1890ff;
  color: white;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
  transform: translateY(-1px);
}

/* 文字按钮 */
.btn-text {
  background: transparent;
  color: #1890ff;
  border-color: transparent;
  box-shadow: none;
}

.btn-text:hover:not(:disabled) {
  background: rgba(24, 144, 255, 0.08);
  color: #40a9ff;
  border-color: transparent;
}

/* 全局表单样式 */
.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  margin-bottom: 6px;
  color: #333;
  font-weight: 500;
  font-size: 14px;
}

.form-group .required {
  color: #ff4d4f;
}

.form-control {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  font-size: 14px;
  transition: all 0.3s ease;
  box-sizing: border-box;
}

.form-control:focus {
  outline: none;
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.form-control::placeholder {
  color: #bfbfbf;
}

/* 搜索框样式 */
.search-box {
  position: relative;
  display: inline-block;
}

.search-box i {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #999;
  font-size: 14px;
  pointer-events: none;
}

.search-box input {
  padding-left: 36px;
}

/* 状态标签 */
.status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  text-align: center;
  line-height: 1;
}

.status-badge.active {
  background: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

.status-badge.inactive {
  background: #fff2f0;
  color: #ff4d4f;
  border: 1px solid #ffa39e;
}

.status-badge.published {
  background: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

.status-badge.draft {
  background: #fff7e6;
  color: #fa8c16;
  border: 1px solid #ffd591;
}

.status-badge.offline {
  background: #f5f5f5;
  color: #999;
  border: 1px solid #d9d9d9;
}

/* 工具提示 */
.tooltip {
  position: relative;
  cursor: help;
}

.tooltip:hover::after {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: #333;
  color: white;
  padding: 6px 8px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  z-index: 1000;
  margin-bottom: 5px;
}

.tooltip:hover::before {
  content: '';
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  border: 4px solid transparent;
  border-top-color: #333;
  margin-bottom: 1px;
}
/* ========================================
   现代化组件样式系统
   ======================================== */

/* 按钮系统 - 更新为新的设计风格 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-sm);
  padding: var(--space-sm) var(--space-md);
  border: 1px solid transparent;
  border-radius: var(--radius-base);
  font-family: var(--font-family);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  line-height: 1.4;
  cursor: pointer;
  transition: all var(--transition-base);
  text-decoration: none;
  white-space: nowrap;
  user-select: none;
  outline: none;
  box-sizing: border-box;
  position: relative;
  overflow: hidden;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left var(--transition-slow);
}

.btn:hover::before {
  left: 100%;
}

.btn:focus {
  box-shadow: 0 0 0 2px var(--primary-light);
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: none !important;
}

.btn:disabled::before {
  display: none;
}

/* 按钮尺寸 */
.btn-xs {
  padding: var(--space-xs) var(--space-sm);
  font-size: var(--font-size-xs);
  border-radius: var(--radius-sm);
}

.btn-sm {
  padding: var(--space-sm) var(--space-base);
  font-size: var(--font-size-xs);
}

.btn-lg {
  padding: var(--space-base) var(--space-xl);
  font-size: var(--font-size-base);
  border-radius: var(--radius-md);
}

.btn-xl {
  padding: var(--space-md) var(--space-2xl);
  font-size: var(--font-size-lg);
  border-radius: var(--radius-md);
}

/* 主要按钮 - 淡蓝科技感 */
.btn-primary {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
  color: var(--bg-primary);
  border-color: var(--primary-color);
  box-shadow: var(--shadow-sm);
}

.btn-primary:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--primary-hover), var(--primary-color));
  border-color: var(--primary-hover);
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.btn-primary:active:not(:disabled) {
  background: linear-gradient(135deg, var(--primary-active), var(--primary-color));
  border-color: var(--primary-active);
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

/* 次要按钮 */
.btn-secondary {
  background: var(--bg-primary);
  color: var(--text-secondary);
  border-color: var(--border-primary);
  box-shadow: var(--shadow-xs);
}

.btn-secondary:hover:not(:disabled) {
  background: var(--bg-secondary);
  border-color: var(--text-tertiary);
  color: var(--text-primary);
  box-shadow: var(--shadow-sm);
  transform: translateY(-1px);
}

/* 成功按钮 */
.btn-success {
  background: linear-gradient(135deg, var(--success-color), #73d13d);
  color: var(--bg-primary);
  border-color: var(--success-color);
  box-shadow: var(--shadow-sm);
}

.btn-success:hover:not(:disabled) {
  background: linear-gradient(135deg, #73d13d, #95de64);
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

/* 警告按钮 */
.btn-warning {
  background: linear-gradient(135deg, var(--warning-color), #ffc53d);
  color: var(--bg-primary);
  border-color: var(--warning-color);
  box-shadow: var(--shadow-sm);
}

.btn-warning:hover:not(:disabled) {
  background: linear-gradient(135deg, #ffc53d, #ffd666);
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

/* 危险按钮 */
.btn-danger {
  background: linear-gradient(135deg, var(--error-color), #ff7875);
  color: var(--bg-primary);
  border-color: var(--error-color);
  box-shadow: var(--shadow-sm);
}

.btn-danger:hover:not(:disabled) {
  background: linear-gradient(135deg, #ff7875, #ffa39e);
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

/* 轮廓按钮 */
.btn-outline {
  background: transparent;
  color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn-outline:hover:not(:disabled) {
  background: var(--primary-color);
  color: var(--bg-primary);
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

/* 文字按钮 */
.btn-text {
  background: transparent;
  color: var(--primary-color);
  border-color: transparent;
  box-shadow: none;
}

.btn-text:hover:not(:disabled) {
  background: var(--primary-light);
  color: var(--primary-hover);
  border-color: transparent;
}

/* 表单系统 */
.form-group {
  margin-bottom: var(--space-md);
}

.form-label {
  display: block;
  margin-bottom: var(--space-sm);
  color: var(--text-primary);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
}

.form-label.required::after {
  content: ' *';
  color: var(--error-color);
}

.form-control {
  width: 100%;
  padding: var(--space-sm) var(--space-base);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-base);
  font-family: var(--font-family);
  font-size: var(--font-size-sm);
  color: var(--text-primary);
  background-color: var(--bg-primary);
  transition: all var(--transition-base);
  box-sizing: border-box;
}

.form-control:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px var(--primary-light);
}

.form-control::placeholder {
  color: var(--text-quaternary);
}

.form-control:disabled {
  background-color: var(--bg-secondary);
  color: var(--text-disabled);
  cursor: not-allowed;
}

/* 表单验证状态 */
.form-control.is-valid {
  border-color: var(--success-color);
}

.form-control.is-valid:focus {
  border-color: var(--success-color);
  box-shadow: 0 0 0 2px var(--success-light);
}

.form-control.is-invalid {
  border-color: var(--error-color);
}

.form-control.is-invalid:focus {
  border-color: var(--error-color);
  box-shadow: 0 0 0 2px var(--error-light);
}

.valid-feedback {
  display: block;
  margin-top: var(--space-xs);
  font-size: var(--font-size-xs);
  color: var(--success-color);
}

.invalid-feedback {
  display: block;
  margin-top: var(--space-xs);
  font-size: var(--font-size-xs);
  color: var(--error-color);
}

/* 搜索框样式 */
.search-box {
  position: relative;
  display: inline-block;
  width: 100%;
}

.search-box .search-icon {
  position: absolute;
  left: var(--space-base);
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-tertiary);
  font-size: var(--font-size-sm);
  pointer-events: none;
  z-index: 1;
}

.search-box .form-control {
  padding-left: var(--space-2xl);
}

.search-box .clear-btn {
  position: absolute;
  right: var(--space-base);
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--text-tertiary);
  cursor: pointer;
  padding: var(--space-xs);
  border-radius: var(--radius-sm);
  transition: all var(--transition-base);
}

.search-box .clear-btn:hover {
  color: var(--text-secondary);
  background: var(--bg-secondary);
}

/* 卡片系统 */
.card {
  background: var(--bg-primary);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  transition: all var(--transition-base);
}

.card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.card-header {
  padding: var(--space-lg) var(--space-xl);
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-secondary);
}

.card-title {
  margin: 0;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

.card-subtitle {
  margin: var(--space-xs) 0 0 0;
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.card-body {
  padding: var(--space-xl);
}

.card-text {
  margin: 0 0 var(--space-md) 0;
  color: var(--text-secondary);
  line-height: 1.6;
}

.card-footer {
  padding: var(--space-lg) var(--space-xl);
  background: var(--bg-tertiary);
  border-top: 1px solid var(--border-secondary);
}

/* 徽章系统 */
.badge {
  display: inline-flex;
  align-items: center;
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  line-height: 1;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
}

.badge-primary {
  background: var(--primary-light);
  color: var(--primary-color);
}

.badge-secondary {
  background: var(--bg-secondary);
  color: var(--text-secondary);
}

.badge-success {
  background: var(--success-light);
  color: var(--success-color);
}

.badge-warning {
  background: var(--warning-light);
  color: var(--warning-color);
}

.badge-error {
  background: var(--error-light);
  color: var(--error-color);
}

.badge-info {
  background: var(--info-light);
  color: var(--info-color);
}

/* 状态徽章 */
.status-badge {
  display: inline-flex;
  align-items: center;
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  line-height: 1;
  text-align: center;
  border: 1px solid transparent;
}

.status-badge.active {
  background: var(--success-light);
  color: var(--success-color);
  border-color: rgba(82, 196, 26, 0.2);
}

.status-badge.inactive {
  background: var(--error-light);
  color: var(--error-color);
  border-color: rgba(255, 77, 79, 0.2);
}

.status-badge.published {
  background: var(--success-light);
  color: var(--success-color);
  border-color: rgba(82, 196, 26, 0.2);
}

.status-badge.draft {
  background: var(--warning-light);
  color: var(--warning-color);
  border-color: rgba(250, 173, 20, 0.2);
}

.status-badge.offline {
  background: var(--bg-secondary);
  color: var(--text-tertiary);
  border-color: var(--border-primary);
}

/* 表格系统 */
.table {
  width: 100%;
  border-collapse: collapse;
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.table th {
  background: var(--bg-secondary);
  padding: var(--space-md) var(--space-lg);
  text-align: left;
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  font-size: var(--font-size-sm);
  border-bottom: 1px solid var(--border-secondary);
}

.table td {
  padding: var(--space-md) var(--space-lg);
  border-bottom: 1px solid var(--border-tertiary);
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
}

.table tbody tr:hover {
  background: var(--bg-tertiary);
}

.table tbody tr:last-child td {
  border-bottom: none;
}

/* 消息提示系统 */
.message-toast {
  display: flex;
  align-items: flex-start;
  padding: var(--space-md);
  border-radius: var(--radius-md);
  margin-bottom: var(--space-md);
  border: 1px solid transparent;
  position: relative;
  animation: slideInRight 0.3s ease-out;
}

.message-toast.success {
  background: var(--success-light);
  border-color: rgba(82, 196, 26, 0.2);
  color: var(--success-color);
}

.message-toast.warning {
  background: var(--warning-light);
  border-color: rgba(250, 173, 20, 0.2);
  color: var(--warning-color);
}

.message-toast.error {
  background: var(--error-light);
  border-color: rgba(255, 77, 79, 0.2);
  color: var(--error-color);
}

.message-toast.info {
  background: var(--info-light);
  border-color: rgba(24, 144, 255, 0.2);
  color: var(--info-color);
}

.message-content {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

/* 加载动画 */
.spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid var(--border-tertiary);
  border-radius: var(--radius-full);
  border-top-color: var(--primary-color);
  animation: spin 1s linear infinite;
}

.spinner-sm {
  width: 16px;
  height: 16px;
  border-width: 1.5px;
}

.spinner-lg {
  width: 24px;
  height: 24px;
  border-width: 3px;
}

/* 工具提示 */
.tooltip {
  position: relative;
  cursor: help;
}

.tooltip::before,
.tooltip::after {
  position: absolute;
  opacity: 0;
  pointer-events: none;
  transition: all var(--transition-base);
  z-index: var(--z-tooltip);
}

.tooltip::before {
  content: attr(data-tooltip);
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%) translateY(-4px);
  background: var(--text-primary);
  color: var(--bg-primary);
  padding: var(--space-sm) var(--space-base);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  white-space: nowrap;
  box-shadow: var(--shadow-md);
}

.tooltip::after {
  content: '';
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  border: 4px solid transparent;
  border-top-color: var(--text-primary);
}

.tooltip:hover::before,
.tooltip:hover::after {
  opacity: 1;
  transform: translateX(-50%) translateY(0);
}

/* 布局辅助类 */
.d-flex {
  display: flex;
}

.d-inline-flex {
  display: inline-flex;
}

.flex-column {
  flex-direction: column;
}

.align-items-center {
  align-items: center;
}

.justify-content-center {
  justify-content: center;
}

.justify-content-between {
  justify-content: space-between;
}

.gap-xs { gap: var(--space-xs); }
.gap-sm { gap: var(--space-sm); }
.gap-base { gap: var(--space-base); }
.gap-md { gap: var(--space-md); }
.gap-lg { gap: var(--space-lg); }
.gap-xl { gap: var(--space-xl); }

/* 文字辅助类 */
.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-tertiary { color: var(--text-tertiary); }
.text-success { color: var(--success-color); }
.text-warning { color: var(--warning-color); }
.text-error { color: var(--error-color); }
.text-info { color: var(--info-color); }

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.font-weight-light { font-weight: var(--font-weight-light); }
.font-weight-normal { font-weight: var(--font-weight-normal); }
.font-weight-medium { font-weight: var(--font-weight-medium); }
.font-weight-semibold { font-weight: var(--font-weight-semibold); }
.font-weight-bold { font-weight: var(--font-weight-bold); }

/* 动画定义 */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
    transform: translateY(0);
  }
  to {
    opacity: 0;
    transform: translateY(-10px);
  }
}

/* 过渡动画类 */
.fade-enter-active,
.fade-leave-active {
  transition: all var(--transition-base);
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    padding: 0 var(--space-md);
  }
  
  .card {
    margin-bottom: var(--space-md);
  }
  
  .card-header,
  .card-body,
  .card-footer {
    padding: var(--space-md);
  }
  
  .table th,
  .table td {
    padding: var(--space-sm) var(--space-md);
  }
  
  .btn-lg {
    padding: var(--space-sm) var(--space-lg);
    font-size: var(--font-size-sm);
  }
}

@media (max-width: 480px) {
  .btn {
    width: 100%;
    justify-content: center;
  }
  
  .d-flex {
    flex-direction: column;
  }
  
  .gap-md {
    gap: var(--space-sm);
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  :root {
    --border-primary: #000000;
    --border-secondary: #000000;
    --text-tertiary: #000000;
  }
  
  .card,
  .table,
  .form-control {
    border: 2px solid var(--border-primary);
  }
  
  .btn {
    border: 2px solid currentColor;
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
  
  .btn::before {
    display: none;
  }
}

/* 暗色模式支持 */
@media (prefers-color-scheme: dark) {
  :root {
    --text-primary: #FFFFFF;
    --text-secondary: #B3B3B3;
    --text-tertiary: #808080;
    --text-quaternary: #4D4D4D;
    --bg-primary: #1A1A1A;
    --bg-secondary: #2D2D2D;
    --bg-tertiary: #404040;
    --bg-layout: #0D0D0D;
    --border-primary: #404040;
    --border-secondary: #2D2D2D;
    --border-tertiary: #1A1A1A;
  }
}
