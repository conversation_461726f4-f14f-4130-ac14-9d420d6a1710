/*
 * Copyright 2025 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.alibaba.cloud.ai.config;

import com.alibaba.cloud.ai.service.code.CodePoolExecutorService;
import com.alibaba.cloud.ai.service.code.CodePoolExecutorServiceFactory;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @since 2025/7/14
 */
@Configuration
@EnableConfigurationProperties(CodeExecutorProperties.class)
public class CodeExecutorConfiguration {

	@Bean
	public CodePoolExecutorService containerPoolExecutor(CodeExecutorProperties properties,
			ChatClient.Builder chatClientBuilder) {
		return CodePoolExecutorServiceFactory.newInstance(properties, chatClientBuilder);
	}

}
