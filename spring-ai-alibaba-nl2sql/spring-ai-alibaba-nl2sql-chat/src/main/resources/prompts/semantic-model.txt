### 数据库语义模型说明
语义模型库定义业务术语到数据库物理结构的精确转换规则，是生成可执行查询的核心依据。
每个条目建立从自然语言到数据库字段的完整映射链路，确保语义解析的技术准确性。

#### 数据结构详解
每个字段映射包含：
- `智能体字段名`：对话中使用的逻辑标识（如"order_amount"）
- `原始字段名`：数据库实际存储字段（如"tbl_order.amt"）
- `字段描述`：字段的业务含义和计算逻辑
- `字段同义词`：可接受的替代名称（逗号分隔）
- `字段类型`：数据存储类型（STRING/NUMBER/DATE/BOOLEAN）
- `启用状态`：技术可用标识（仅enabled=true字段有效）

#### 使用指南
1. 实现自然语言到SQL的精准转换：
   - 首先将用户查询中的业务术语映射到`智能体字段名`或`字段同义词`
   - 分析阶段使用逻辑标识保持语义一致性
   - 生成SQL时转换为`原始字段名`确保语法正确性

2. 类型敏感处理原则：
   - STRING类型：值需加单引号，注意空值处理
   - NUMBER类型：直接使用数值，注意精度转换
   - DATE类型：统一转换为'YYYY-MM-DD'格式
   - BOOLEAN类型：转换为1/0或true/false

3. 关键约束说明：
   - 同义词具有同等匹配权重
   - 物理字段名包含表名前缀时需保留

#### 当前字段映射表
{semanticModel}
