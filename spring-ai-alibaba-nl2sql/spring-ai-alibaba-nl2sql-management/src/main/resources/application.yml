server:
  port: 8065
spring:
  datasource:
    url: ****************************************************************************************************************************************************************************************************************************************
    username: ${MYSQL_USERNAME:root}
    password: ${MYSQL_PASSWORD:root}
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.alibaba.druid.pool.DruidDataSource
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        format_sql: true
        dialect: org.hibernate.dialect.MySQLDialect
    defer-datasource-initialization: true
  sql:
    init:
      mode: always
      schema-locations: classpath:sql/schema.sql
      data-locations: classpath:sql/data.sql
      continue-on-error: true
      separator: ;
      encoding: utf-8

  ai:
    mcp:
      server:
        name: xiyan-server    # MCP服务器名称
        version: 0.0.1           # 服务器版本号
    vectorstore:
      analytic:
        enabled: false
        collectName: chatBi
        regionId:
        dbInstanceId:
        managerAccount:
        managerAccountPassword:
        namespace: chat
        namespacePassword: chat
        defaultTopK: 6
        defaultSimilarityThreshold: 0.75
        accessKeyId:
        accessKeySecret:
    openai:
      base-url: https://dashscope.aliyuncs.com/compatible-mode
      api-key: ${AI_DASHSCOPE_API_KEY}
      model: qwen-max
      embedding:
        model: text-embedding-v4

    alibaba:
      nl2sql:
        code-executor:
          code-pool-executor: ai_simulation

chatBi:
  dbConfig:
    url: *********************************************************************************************************************************************************************************************************************************************************************
    username: ${MYSQL_USERNAME:root}
    password: ${MYSQL_PASSWORD:root}
    schema:
    connection-type: jdbc
    dialect-type: mysql
