#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Spring AI Alibaba Jmanus 中文内容分析工具 - 修正版本
修正了误识别Apache License中"AS IS"等英文内容为中文的问题
"""

import os
import re
import json
import argparse
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Set
from collections import defaultdict

class CorrectedChineseAnalyzer:
    def __init__(self, target_dir: str):
        self.target_dir = Path(target_dir)
        # 只检测真正的中文字符，不包括中文标点符号（避免误识别）
        self.chinese_pattern = re.compile(r'[\u4e00-\u9fff]')
        # 中文标点符号单独检测
        self.chinese_punctuation = re.compile(r'，。！？；：“”（）【】《》')

        # 排除常见的英文短语，避免误识别
        self.exclude_patterns = [
            r'\bAS IS\b',  # Apache License中的"AS IS"
            r'\bIS NULL\b',  # SQL中的"IS NULL"
            r'\bIS NOT\b',  # SQL中的"IS NOT"
            r'@author\s+\w+',  # 作者信息
            r'@time\s+\d{4}/\d{1,2}/\d{1,2}',  # 时间信息
        ]

        self.results = {
            'total_files': 0,
            'files_with_chinese': 0,
            'chinese_files': [],
            'analysis_summary': {
                'comments_only': 0,
                'strings_only': 0,
                'identifiers_only': 0,
                'mixed_content': 0
            },
            'detailed_stats': defaultdict(int)
        }

    def has_real_chinese_content(self, text: str) -> bool:
        """检查文本是否包含真正的中文内容（排除误识别）"""
        # 先检查是否有中文字符或中文标点
        if not (self.chinese_pattern.search(text) or self.chinese_punctuation.search(text)):
            return False

        # 排除常见的英文短语
        for pattern in self.exclude_patterns:
            if re.search(pattern, text, re.IGNORECASE):
                # 如果匹配到排除模式，进一步检查是否真的包含中文
                temp_text = re.sub(pattern, '', text, flags=re.IGNORECASE)
                if not (self.chinese_pattern.search(temp_text) or self.chinese_punctuation.search(temp_text)):
                    return False

        return True

    def analyze_file_content(self, file_path: Path) -> Dict:
        """分析单个文件的中文内容"""
        result = {
            'path': str(file_path.relative_to(self.target_dir)),
            'absolute_path': str(file_path),
            'has_chinese': False,
            'chinese_in_comments': False,
            'chinese_in_strings': False,
            'chinese_in_identifiers': False,
            'chinese_lines': [],
            'total_lines': 0
        }

        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()
                result['total_lines'] = len(lines)

                in_multiline_comment = False

                for line_num, line in enumerate(lines, 1):
                    original_line = line
                    line = line.strip()
                    if not line:
                        continue

                    # 检查是否包含真正的中文内容
                    if not self.has_real_chinese_content(line):
                        continue

                    result['has_chinese'] = True
                    result['chinese_lines'].append({
                        'line_number': line_num,
                        'content': line[:100] + '...' if len(line) > 100 else line
                    })

                    # 分析中文出现的位置类型
                    # 检查多行注释
                    if '/*' in line:
                        in_multiline_comment = True
                    if in_multiline_comment:
                        if self.has_real_chinese_content(line):
                            result['chinese_in_comments'] = True
                        if '*/' in line:
                            in_multiline_comment = False
                        continue

                    # 检查单行注释
                    if '//' in line:
                        comment_part = line[line.find('//'):]
                        if self.has_real_chinese_content(comment_part):
                            result['chinese_in_comments'] = True
                        continue

                    # 检查字符串字面量
                    string_matches = re.finditer(r'"([^"]*)"', line)
                    for match in string_matches:
                        if self.has_real_chinese_content(match.group(1)):
                            result['chinese_in_strings'] = True
                            break

                    # 检查字符字面量
                    char_matches = re.finditer(r"'([^']*)'", line)
                    for match in char_matches:
                        if self.has_real_chinese_content(match.group(1)):
                            result['chinese_in_strings'] = True
                            break

                    # 检查标识符（简单检查）
                    # 移除字符串和注释后检查剩余部分
                    temp_line = re.sub(r'"[^"]*"', '', line)  # 移除字符串
                    temp_line = re.sub(r"'[^']*'", '', temp_line)  # 移除字符
                    temp_line = re.sub(r'//.*$', '', temp_line)  # 移除单行注释

                    if self.has_real_chinese_content(temp_line):
                        result['chinese_in_identifiers'] = True

        except Exception as e:
            print(f"警告：无法读取文件 {file_path}: {e}")

        return result

    def analyze_directory(self) -> None:
        """分析整个目录"""
        if not self.target_dir.exists():
            raise FileNotFoundError(f"目录不存在: {self.target_dir}")

        java_files = list(self.target_dir.rglob("*.java"))
        self.results['total_files'] = len(java_files)

        print(f"找到 {len(java_files)} 个 Java 文件，开始分析...")

        for i, java_file in enumerate(java_files, 1):
            if i % 50 == 0 or i == len(java_files):
                print(f"分析进度: {i}/{len(java_files)}")

            file_result = self.analyze_file_content(java_file)

            if file_result['has_chinese']:
                self.results['files_with_chinese'] += 1
                self.results['chinese_files'].append(file_result)

                # 统计内容类型
                content_types = []
                if file_result['chinese_in_comments']:
                    content_types.append('comments')
                    self.results['detailed_stats']['chinese_in_comments'] += 1
                if file_result['chinese_in_strings']:
                    content_types.append('strings')
                    self.results['detailed_stats']['chinese_in_strings'] += 1
                if file_result['chinese_in_identifiers']:
                    content_types.append('identifiers')
                    self.results['detailed_stats']['chinese_in_identifiers'] += 1

                # 分类统计
                if len(content_types) == 1:
                    if 'comments' in content_types:
                        self.results['analysis_summary']['comments_only'] += 1
                    elif 'strings' in content_types:
                        self.results['analysis_summary']['strings_only'] += 1
                    elif 'identifiers' in content_types:
                        self.results['analysis_summary']['identifiers_only'] += 1
                else:
                    self.results['analysis_summary']['mixed_content'] += 1

    def generate_report(self) -> str:
        """生成分析报告"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        report_lines = [
            "=" * 70,
            "Spring AI Alibaba Jmanus 中文内容分析报告（修正版）",
            "=" * 70,
            f"生成时间: {timestamp}",
            f"分析目录: {self.target_dir}",
            "",
            "=== 统计结果 ===",
            f"总计 Java 文件数: {self.results['total_files']}",
            f"包含中文内容的文件数: {self.results['files_with_chinese']}",
        ]

        if self.results['files_with_chinese'] > 0:
            percentage = (self.results['files_with_chinese'] / self.results['total_files']) * 100
            report_lines.extend([
                f"包含中文内容的文件占比: {percentage:.2f}%",
                "",
                "=== 中文内容分布分析 ===",
                f"仅在注释中包含中文: {self.results['analysis_summary']['comments_only']} 个文件",
                f"仅在字符串中包含中文: {self.results['analysis_summary']['strings_only']} 个文件",
                f"仅在标识符中包含中文: {self.results['analysis_summary']['identifiers_only']} 个文件",
                f"混合类型（多种位置）: {self.results['analysis_summary']['mixed_content']} 个文件",
                "",
                "=== 详细统计 ===",
                f"包含中文注释的文件: {self.results['detailed_stats']['chinese_in_comments']}",
                f"包含中文字符串的文件: {self.results['detailed_stats']['chinese_in_strings']}",
                f"包含中文标识符的文件: {self.results['detailed_stats']['chinese_in_identifiers']}",
                "",
                "=== 包含中文内容的文件列表 ===",
            ])

            for file_info in self.results['chinese_files']:
                content_types = []
                if file_info['chinese_in_comments']:
                    content_types.append("注释")
                if file_info['chinese_in_strings']:
                    content_types.append("字符串")
                if file_info['chinese_in_identifiers']:
                    content_types.append("标识符")

                report_lines.extend([
                    f"\n文件: {file_info['path']}",
                    f"  中文内容类型: {', '.join(content_types)}",
                    f"  包含中文的行数: {len(file_info['chinese_lines'])}",
                    "  示例行:"
                ])

                # 显示前3行示例
                for line_info in file_info['chinese_lines'][:3]:
                    report_lines.append(f"    第{line_info['line_number']}行: {line_info['content']}")

                if len(file_info['chinese_lines']) > 3:
                    report_lines.append(f"    ... 还有 {len(file_info['chinese_lines']) - 3} 行")
        else:
            report_lines.append("✓ 未发现包含中文内容的 Java 文件")

        return "\n".join(report_lines)

    def save_report(self, filename: str = None) -> str:
        """保存报告到文件"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"corrected_chinese_analysis_report_{timestamp}.txt"

        report_content = self.generate_report()

        with open(filename, 'w', encoding='utf-8') as f:
            f.write(report_content)

        return filename

def main():
    parser = argparse.ArgumentParser(description='准确分析Java代码中的中文内容（修正版）')
    parser.add_argument('--dir', '-d', default='spring-ai-alibaba-jmanus/src/main/java',
                       help='要分析的目录路径')
    parser.add_argument('--output', '-o', help='输出报告文件名')
    parser.add_argument('--verbose', '-v', action='store_true', help='详细输出')

    args = parser.parse_args()

    try:
        analyzer = CorrectedChineseAnalyzer(args.dir)
        analyzer.analyze_directory()

        # 打印结果摘要
        print("\n" + "=" * 50)
        print("分析完成！")
        print(f"总计文件数: {analyzer.results['total_files']}")
        print(f"包含中文的文件数: {analyzer.results['files_with_chinese']}")

        if analyzer.results['files_with_chinese'] > 0:
            percentage = (analyzer.results['files_with_chinese'] / analyzer.results['total_files']) * 100
            print(f"包含中文的文件占比: {percentage:.2f}%")

        # 保存报告
        report_file = analyzer.save_report(args.output)
        print(f"详细报告已保存到: {report_file}")

        # 如果启用详细模式，打印完整报告
        if args.verbose:
            print("\n" + analyzer.generate_report())

    except Exception as e:
        print(f"错误: {e}")
        return 1

    return 0

if __name__ == "__main__":
    exit(main())
